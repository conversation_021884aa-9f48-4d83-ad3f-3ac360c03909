# Careers Page - 招聘信息页面

## 概述

已成功创建了完整的careers（招聘信息）页面，包含中英文双语支持和丰富的mock演示数据。

## 页面结构

### 1. 页面组件
- `app/[locale]/careers/page.tsx` - 主页面文件，包含SEO元数据
- `app/[locale]/careers/components/CareersClient.tsx` - 主要客户端组件
- `app/[locale]/careers/components/CareersHero.tsx` - 英雄区域组件
- `app/[locale]/careers/components/WhyJoinUs.tsx` - 为什么加入我们组件
- `app/[locale]/careers/components/OpenPositions.tsx` - 开放职位组件
- `app/[locale]/careers/components/JobDetailModal.tsx` - 职位详情弹窗组件
- `app/[locale]/careers/components/ApplicationProcess.tsx` - 申请流程组件
- `app/[locale]/careers/components/CareersCTA.tsx` - 行动号召组件

### 2. 国际化文件
- `messages/modules/zh/careers.json` - 中文翻译文件
- `messages/modules/en/careers.json` - 英文翻译文件
- 已更新导航文件添加careers链接

## 功能特性

### 1. 英雄区域 (CareersHero)
- 吸引人的标题和描述
- 公司统计数据展示
- 平滑滚动到职位列表
- 动画效果和渐变背景

### 2. 为什么加入我们 (WhyJoinUs)
- 6个核心优势展示
- 图标和描述卡片
- 额外福利待遇列表
- 响应式网格布局

### 3. 开放职位 (OpenPositions)
- 6个mock职位数据：
  - 高级AI工程师
  - 云架构师
  - 前端开发工程师
  - 产品经理
  - DevOps工程师
  - 数据科学家
- 职位卡片包含：
  - 职位标题和描述
  - 地点、类型、经验、薪资
  - 技能标签
  - 查看详情和立即申请按钮

### 4. 职位详情弹窗 (JobDetailModal)
- 完整的职位信息展示
- 任职要求列表
- 工作职责列表
- 申请功能（邮件链接）

### 5. 申请流程 (ApplicationProcess)
- 5步申请流程可视化
- 面试小贴士
- 清晰的时间线展示

### 6. 联系方式 (CareersCTA)
- 邮件和电话联系方式
- 一键发送简历功能
- 额外联系信息

## Mock数据详情

### 职位列表包含：
1. **高级AI工程师** - 北京，25K-40K，AI/机器学习
2. **云架构师** - 上海，30K-50K，云计算/Kubernetes
3. **前端开发工程师** - 深圳，18K-30K，React/Vue.js
4. **产品经理** - 北京，20K-35K，产品管理/B2B
5. **DevOps工程师** - 杭州，22K-38K，CI/CD/自动化
6. **数据科学家** - 北京，25K-42K，数据科学/机器学习

### 每个职位包含：
- 详细的职位描述
- 5-6条任职要求
- 5条工作职责
- 相关技能标签
- 薪资范围和工作地点

## 技术实现

### 1. 响应式设计
- 移动端友好的布局
- 网格系统适配不同屏幕
- 触摸友好的交互

### 2. 动画效果
- Framer Motion动画库
- 滚动触发动画
- 平滑的页面过渡

### 3. 交互功能
- 职位详情弹窗
- 邮件申请功能
- 电话联系功能
- 平滑滚动导航

### 4. SEO优化
- 完整的元数据配置
- Open Graph支持
- Twitter卡片支持
- 多语言URL结构

## 访问方式

- 中文版本：`http://localhost:3002/careers`
- 英文版本：`http://localhost:3002/en/careers`

## 导航集成

已将careers链接添加到主导航栏，用户可以通过顶部导航轻松访问招聘页面。

## 联系功能

- **邮箱申请**：点击申请按钮会打开邮件客户端，预填充邮件内容
- **电话联系**：点击电话按钮会尝试拨打电话
- **简历投递**：<EMAIL>

## 未来扩展

1. **后端集成**：可以连接到招聘管理系统
2. **在线申请表单**：添加完整的申请表单
3. **简历上传**：支持文件上传功能
4. **职位搜索**：添加搜索和筛选功能
5. **申请跟踪**：申请状态跟踪系统
