# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Core Commands
- `npm run dev` - Start development server
- `npm run build` - Build the production application
- `npm run start` - Start production server
- `npm run lint` - Run ESLint for code quality checks

### Build and Deployment
The application is configured for SSR (Server-Side Rendering) with Next.js 13.5.1. Static export is disabled to support full SSR capabilities.

## Architecture Overview

### Application Structure
This is a Next.js 13 App Router application with internationalization support using `next-intl`. The application follows a multi-language corporate website architecture.

### Key Architectural Components

1. **Internationalization (i18n)**
   - Supports Chinese (`zh`) and English (`en`) locales
   - Default locale: Chinese (`zh`)
   - Uses `next-intl` with App Router
   - Translation files organized in `/messages/modules/{locale}/` structure
   - Route structure: `/[locale]/page` with middleware handling locale detection

2. **Route Structure**
   - All user-facing routes are under `/app/[locale]/`
   - API routes in `/app/api/`
   - Dynamic locale parameter handles language switching
   - Middleware enforces locale prefix for all routes

3. **Translation System**
   - Modular translation files in `/messages/modules/{locale}/`
   - Build script `/messages/build-messages.js` compiles modules into single locale files
   - Translation functions use dot notation keys (e.g., `products.aiAnnotation.name`)

4. **Product System**
   - Product data centralized in `/lib/products-data.ts`
   - Four core products: AI Annotation, CPU Rental, Education Management, Custom Development
   - Dynamic product pages at `/[locale]/products/[slug]/`
   - Product data uses translation functions for multi-language support

5. **Component Architecture**
   - Client components suffixed with `Client` (e.g., `AboutClient.tsx`)
   - Shared components in `/components/`
   - Page-specific components in feature directories
   - Uses Radix UI components extensively for UI primitives

### Technology Stack
- **Framework**: Next.js 13.5.1 with App Router
- **Styling**: Tailwind CSS with custom animations
- **UI Components**: Radix UI primitives
- **Forms**: React Hook Form with Zod validation
- **Icons**: Lucide React
- **Animations**: Framer Motion
- **Type Safety**: TypeScript with strict configuration

### Important Configuration Files
- `next.config.js` - Optimized for SSR with webpack optimizations and security headers
- `middleware.ts` - Handles internationalization routing
- `i18n/request.ts` - Next-intl configuration for server-side translation loading
- `lib/i18n.ts` - Core i18n utilities and locale management

### SEO and Performance
- Configured for production SEO with security headers
- Image optimization enabled for multiple formats (WebP, AVIF)
- Bundle splitting and webpack optimizations configured
- Sitemap and robots.txt generation implemented

### API Structure
- RESTful API routes for products, news, and contact
- Dynamic API routes support locale-specific data
- Contact form API with validation

## Development Notes

### Internationalization Workflow
When adding new translations:
1. Add keys to appropriate module files in `/messages/modules/{locale}/`
2. Run build script to compile modules into main locale files
3. Use translation keys in components via `useTranslations()` hook

### Product Management
Product data is centralized and uses translation functions. When adding new products:
1. Update product data in `/lib/products-data.ts`
2. Add corresponding translation keys to product module files
3. Create product detail pages following existing pattern

### Component Patterns
- Server components for static content and data fetching
- Client components for interactive elements (marked with `"use client"`)
- Consistent use of TypeScript interfaces for props
- Tailwind classes with design system consistency