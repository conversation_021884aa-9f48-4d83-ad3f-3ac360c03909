# 国际化翻译文件模块化管理

## 概述

本项目已将原本的大型翻译文件（`en.json` 和 `zh.json`）拆分为小的模块化文件，以提高可维护性和团队协作效率。

## 文件结构

```
messages/
├── modules/
│   ├── en/                     # 英文翻译模块
│   │   ├── common.json         # 通用翻译
│   │   ├── navigation.json     # 导航翻译
│   │   ├── hero.json          # 首页英雄区翻译
│   │   ├── services.json      # 服务翻译
│   │   ├── products.json      # 产品翻译
│   │   ├── product-details.json # 产品详情翻译
│   │   ├── stats.json         # 统计翻译
│   │   ├── home.json          # 首页翻译
│   │   ├── about.json         # 关于页面翻译
│   │   ├── products-page.json # 产品页面翻译
│   │   ├── ai-annotation.json # AI标注页面翻译
│   │   ├── cpu-rental.json    # CPU租用页面翻译
│   │   ├── education-management.json # 教育管理页面翻译
│   │   ├── custom-development.json # 定制开发页面翻译
│   │   ├── contact.json       # 联系页面翻译
│   │   └── footer.json        # 页脚翻译
│   └── zh/                     # 中文翻译模块（结构相同）
│       ├── common.json
│       ├── navigation.json
│       └── ...
├── build-messages.js           # 构建脚本
├── en.json                     # 生成的英文翻译文件
└── zh.json                     # 生成的中文翻译文件
```

## 模块说明

### 核心模块

1. **common.json** - 通用翻译，包含按钮、标签等常用文本
2. **navigation.json** - 导航菜单翻译
3. **hero.json** - 首页英雄区翻译
4. **services.json** - 服务介绍翻译
5. **products.json** - 产品列表翻译
6. **product-details.json** - 产品详情翻译
7. **stats.json** - 统计数据翻译

### 页面模块

8. **home.json** - 首页特定翻译
9. **about.json** - 关于页面翻译
10. **products-page.json** - 产品页面翻译
11. **contact.json** - 联系页面翻译
12. **footer.json** - 页脚翻译

### 产品页面模块

13. **ai-annotation.json** - AI标注产品页面翻译
14. **cpu-rental.json** - CPU租用产品页面翻译
15. **education-management.json** - 教育管理产品页面翻译
16. **custom-development.json** - 定制开发产品页面翻译

## 使用方法

### 1. 编辑翻译

要修改翻译内容，请编辑对应的模块文件：

```bash
# 编辑英文通用翻译
messages/modules/en/common.json

# 编辑中文产品翻译
messages/modules/zh/products.json
```

### 2. 构建翻译文件

修改模块文件后，运行构建脚本生成最终的翻译文件：

```bash
cd messages
node build-messages.js
```

这将生成：
- `messages/en.json` - 完整的英文翻译文件
- `messages/zh.json` - 完整的中文翻译文件

### 3. 添加新模块

要添加新的翻译模块：

1. 在 `messages/modules/en/` 和 `messages/modules/zh/` 中创建新的 JSON 文件
2. 在 `build-messages.js` 的 `modules` 数组中添加新模块名
3. 运行构建脚本

## 优势

### 1. 可维护性
- 每个模块专注于特定功能区域
- 文件较小，易于查找和编辑
- 减少合并冲突

### 2. 团队协作
- 不同团队成员可以同时编辑不同模块
- 清晰的职责分工
- 更好的版本控制

### 3. 扩展性
- 易于添加新的语言支持
- 模块化结构便于功能扩展
- 支持按需加载

## 注意事项

1. **始终编辑模块文件**：不要直接编辑生成的 `en.json` 和 `zh.json` 文件
2. **保持结构一致**：确保英文和中文模块的键名结构一致
3. **运行构建脚本**：每次修改模块文件后都要运行构建脚本
4. **测试翻译**：构建后测试应用以确保翻译正确显示

## 构建脚本说明

`build-messages.js` 脚本的功能：

1. 读取所有模块文件
2. 根据模块名称合并数据到正确的命名空间
3. 生成完整的翻译文件
4. 处理特殊的产品页面模块合并逻辑

## 示例工作流程

1. 需要添加新的产品翻译：
   ```bash
   # 1. 编辑产品模块
   vim messages/modules/en/products.json
   vim messages/modules/zh/products.json
   
   # 2. 构建翻译文件
   cd messages && node build-messages.js
   
   # 3. 测试应用
   npm run dev
   ```

2. 需要修改页面翻译：
   ```bash
   # 1. 编辑对应页面模块
   vim messages/modules/en/about.json
   
   # 2. 构建并测试
   cd messages && node build-messages.js
   ```

这种模块化的方式使得翻译管理更加高效和有序。
