"use client"

import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Brain, Cpu, GraduationCap, Code, Phone, Mail, MapPin, ArrowRight } from "lucide-react"
import Link from "next/link"
import { useTranslations } from '@/hooks/useTranslations'

export default function AboutClient() {
  const t = useTranslations('about')
  const tServices = useTranslations('services')

  // Core business data with individual feature keys
  const coreBusinesses = [
    {
      titleKey: "aiAnnotation.title",
      contentKey: "aiAnnotation.description",
      icon: Brain,
      stats: `10M+ ${t('stats.annotatedData')}`,
      features: [
        t('features.aiAnnotation.imageAnnotation'),
        t('features.aiAnnotation.textAnnotation'),
        t('features.aiAnnotation.audioAnnotation'),
        t('features.aiAnnotation.qualityControl')
      ],
      color: "from-blue-500 to-cyan-500",
      bgColor: "from-blue-50 to-cyan-50"
    },
    {
      titleKey: "cpuRental.title",
      contentKey: "cpuRental.description",
      icon: Cpu,
      stats: `50,000+ ${t('stats.cpuCores')}`,
      features: [
        t('features.cpuRental.highPerformance'),
        t('features.cpuRental.elasticScaling'),
        t('features.cpuRental.payAsYouGo'),
        t('features.cpuRental.monitoring')
      ],
      color: "from-green-500 to-emerald-500",
      bgColor: "from-green-50 to-emerald-50"
    },
    {
      titleKey: "education.title",
      contentKey: "education.description",
      icon: GraduationCap,
      stats: `50,000+ ${t('stats.studentsServed')}`,
      features: [
        t('features.education.courseManagement'),
        t('features.education.onlineExams'),
        t('features.education.studentTracking'),
        t('features.education.certificateSystem')
      ],
      color: "from-purple-500 to-violet-500",
      bgColor: "from-purple-50 to-violet-50"
    },
    {
      titleKey: "development.title",
      contentKey: "development.description",
      icon: Code,
      stats: `200+ ${t('stats.successfulProjects')}`,
      features: [
        t('features.development.fullStack'),
        t('features.development.mobileApps'),
        t('features.development.systemIntegration'),
        t('features.development.technicalConsulting')
      ],
      color: "from-emerald-500 to-green-500",
      bgColor: "from-emerald-50 to-green-50"
    }
  ]

  return (
    <div className="min-h-screen">
      {/* Enhanced Hero Section */}
      <section className="relative py-24 sm:py-32 overflow-hidden">
        <div className="absolute inset-0 -z-10">
          <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-primary/3 to-transparent" />
          <div className="absolute top-0 left-1/4 w-96 h-96 bg-primary/10 rounded-full blur-3xl animate-pulse" />
          <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-blue-400/10 rounded-full blur-3xl animate-pulse delay-1000" />
          <div className="grid-bg absolute inset-0 opacity-30" />
        </div>

        <div className="relative mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-4xl text-center">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
              className="mb-6"
            >
              <div className="inline-flex items-center gap-2 px-4 py-2 bg-primary/10 rounded-full border border-primary/20 mb-6">
                <span className="text-2xl">🚀</span>
                <span className="text-sm font-medium text-primary">{t('foundedIn')}</span>
              </div>
            </motion.div>

            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-4xl font-bold tracking-tight sm:text-6xl lg:text-7xl mb-6"
            >
              <span className="text-gradient-modern">{t('title')}</span>
            </motion.h1>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
              className="text-xl leading-8 text-muted-foreground mb-8 max-w-3xl mx-auto"
            >
              {t('description')}
            </motion.p>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="flex flex-wrap justify-center gap-8 text-center"
            >
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
                <div className="text-3xl font-bold text-primary mb-1">2000+</div>
                <div className="text-sm text-muted-foreground">{t('stats.clients')}</div>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
                <div className="text-3xl font-bold text-primary mb-1">200+</div>
                <div className="text-sm text-muted-foreground">{t('stats.team')}</div>
              </div>
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
                <div className="text-3xl font-bold text-primary mb-1">2025</div>
                <div className="text-sm text-muted-foreground">{t('stats.innovation')}</div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Core Businesses Section */}
      <section className="py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            className="mx-auto max-w-3xl text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">
              {t('coreBusinessTitle')}
            </h2>
            <p className="text-lg leading-8 text-muted-foreground">
              {t('coreBusinessSubtitle')}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
            {coreBusinesses.map((business, index) => (
              <motion.div
                key={business.titleKey}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -5 }}
                className="group"
              >
                <Card className="relative overflow-hidden border-0 bg-white/60 backdrop-blur-xl shadow-lg hover:shadow-xl transition-all duration-500 h-full">
                  <div className={`absolute inset-0 bg-gradient-to-br ${business.bgColor} opacity-0 group-hover:opacity-50 transition-opacity duration-500`} />
                  <CardContent className="p-8 relative">
                    <div className="flex items-center gap-4 mb-6">
                      <div className={`rounded-2xl bg-gradient-to-br ${business.color} p-4 shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                        <business.icon className="h-8 w-8 text-white" />
                      </div>
                      <div>
                        <h3 className="text-xl font-semibold group-hover:text-primary transition-colors">{tServices(business.titleKey)}</h3>
                        <p className="text-sm text-primary font-medium">{business.stats}</p>
                      </div>
                    </div>
                    <p className="text-muted-foreground mb-6 leading-relaxed">{tServices(business.contentKey)}</p>
                    <div className="flex flex-wrap gap-2">
                      {business.features.map((feature: string) => (
                        <span
                          key={feature}
                          className="inline-flex items-center rounded-full bg-primary/10 px-3 py-1 text-xs font-medium text-primary border border-primary/20"
                        >
                          {feature}
                        </span>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-24 sm:py-32 bg-gradient-to-br from-primary/5 to-primary/10">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            className="mx-auto max-w-3xl text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-4">
              {t('contact.title')}
            </h2>
            <p className="text-lg leading-8 text-muted-foreground">
              {t('contact.subtitle')}
            </p>
          </motion.div>

          {/* Contact Cards */}
          <div className="grid grid-cols-1 gap-8 sm:grid-cols-3 mb-16">
            {[
              { icon: Phone, labelKey: 'contact.phone.label', valueKey: 'contact.phone.value' },
              { icon: Mail, labelKey: 'contact.email.label', valueKey: 'contact.email.value' },
              { icon: MapPin, labelKey: 'contact.address.label', valueKey: 'contact.address.value' }
            ].map((info, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -5 }}
                className="group"
              >
                <Card className="border-0 bg-white/60 backdrop-blur-xl shadow-lg hover:shadow-xl transition-all duration-500 text-center h-full">
                  <CardContent className="p-8">
                    <div className="mx-auto mb-6 rounded-2xl bg-gradient-to-br from-primary/20 to-primary/10 p-4 w-fit group-hover:scale-110 transition-transform duration-300">
                      <info.icon className="h-6 w-6 text-primary" />
                    </div>
                    <h3 className="text-sm font-semibold text-muted-foreground mb-2">
                      {t(info.labelKey)}
                    </h3>
                    <p className="text-base font-medium">{t(info.valueKey)}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>

          {/* CTA Buttons */}
          <motion.div
            className="flex flex-col sm:flex-row items-center justify-center gap-4"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            viewport={{ once: true }}
          >
            <Link href="/products">
              <Button className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 px-8 py-3 text-lg">
                {t('contact.buttons.viewProducts')}
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
            </Link>
            <Link href="/contact-us">
              <Button className="px-8 py-3 text-lg border border-primary/20 bg-transparent hover:bg-primary/5 text-foreground">
                {t('contact.buttons.contactSales')}
                <Phone className="w-5 h-5 ml-2" />
              </Button>
            </Link>
          </motion.div>
        </div>
      </section>
    </div>
  )
}
