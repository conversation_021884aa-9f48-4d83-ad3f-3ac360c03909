import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { isValidLocale } from '@/lib/i18n'
import AboutClient from './components/AboutClient'

export async function generateMetadata({ params: { locale } }: { params: { locale: string } }): Promise<Metadata> {
  if (!isValidLocale(locale)) {
    notFound();
  }

  const isEnglish = locale === 'en';

  return {
    title: isEnglish
      ? 'About Us - zerodots | Enterprise AI & Cloud Computing Solutions Provider'
      : '关于我们 - zerodots | 企业级AI与云计算解决方案提供商',
    description: isEnglish
      ? 'Learn about zerodots, a leading provider of enterprise AI and cloud computing solutions. Founded in 2025, we specialize in AI annotation, CPU rental, education management, and custom software development.'
      : '了解zerodots，领先的企业级AI与云计算解决方案提供商。成立于2025年，专注于AI智能标注、CPU算力租用、教育培训管理和定制软件开发。',
  };
}

export default function About({ params: { locale } }: { params: { locale: string } }) {
  if (!isValidLocale(locale)) {
    notFound();
  }

  return <AboutClient />;
}