"use client"

import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { 
  FileText, Search, MessageSquare, 
  Users, CheckCircle 
} from "lucide-react"
import { useTranslations, useRawTranslations } from '@/hooks/useTranslations'

export function ApplicationProcess() {
  const t = useTranslations('careers')
  const rawData = useRawTranslations('careers')

  const iconMap = {
    1: FileText,
    2: Search,
    3: MessageSquare,
    4: Users,
    5: CheckCircle
  }

  const steps = rawData.applicationProcess?.steps || []

  return (
    <section className="py-24 sm:py-32">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="mx-auto max-w-2xl text-center mb-16"
        >
          <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
            {t('applicationProcess.title')}
          </h2>
          <p className="mt-6 text-lg leading-8 text-muted-foreground">
            {t('applicationProcess.subtitle')}
          </p>
        </motion.div>

        <div className="relative">
          {/* Connection line */}
          <div className="absolute top-1/2 left-0 right-0 h-0.5 bg-gradient-to-r from-primary/20 via-primary/40 to-primary/20 transform -translate-y-1/2 hidden lg:block" />
          
          <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-5">
            {steps.map((step, index) => {
              const IconComponent = iconMap[step.step as keyof typeof iconMap] || FileText
              
              return (
                <motion.div
                  key={step.step}
                  initial={{ opacity: 0, y: 40 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="relative"
                >
                  <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 bg-background relative z-10">
                    <CardContent className="p-6 text-center">
                      {/* Step number */}
                      <div className="flex items-center justify-center w-12 h-12 rounded-full bg-gradient-to-br from-primary to-primary/80 text-white font-bold text-lg mb-4 mx-auto">
                        {step.step}
                      </div>
                      
                      {/* Icon */}
                      <div className="flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br from-primary/10 to-primary/20 mb-4 mx-auto">
                        <IconComponent className="h-8 w-8 text-primary" />
                      </div>
                      
                      <h3 className="text-lg font-semibold mb-3">
                        {step.title}
                      </h3>
                      
                      <p className="text-sm text-muted-foreground leading-relaxed">
                        {step.description}
                      </p>
                    </CardContent>
                  </Card>
                </motion.div>
              )
            })}
          </div>
        </div>

        {/* Additional info */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          viewport={{ once: true }}
          className="mt-16 text-center"
        >
          <div className="bg-gradient-to-r from-primary/5 to-primary/10 rounded-2xl p-8">
            <h3 className="text-xl font-semibold mb-4">
              面试小贴士
            </h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 text-sm">
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <div className="font-medium mb-1">准备充分</div>
                  <div className="text-muted-foreground">了解公司业务和职位要求</div>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <div className="font-medium mb-1">展示技能</div>
                  <div className="text-muted-foreground">准备相关项目案例和作品</div>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0"></div>
                <div>
                  <div className="font-medium mb-1">积极沟通</div>
                  <div className="text-muted-foreground">主动提问，展现学习热情</div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
