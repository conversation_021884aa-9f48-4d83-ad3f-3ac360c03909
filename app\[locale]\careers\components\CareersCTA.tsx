"use client"

import { motion } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Mail, Phone, ArrowRight, Send } from "lucide-react"
import { useTranslations } from '@/hooks/useTranslations'

export function CareersCTA() {
  const t = useTranslations('careers')

  const handleEmailClick = () => {
    window.open('mailto:<EMAIL>?subject=Career Inquiry&body=Dear HR Team,%0D%0A%0D%0AI am interested in career opportunities at ZeroDots.%0D%0A%0D%0APlease find my resume attached.%0D%0A%0D%0ABest regards')
  }

  const handlePhoneClick = () => {
    window.open('tel:+8640012345678')
  }

  return (
    <section className="py-24 sm:py-32 bg-gradient-to-br from-primary/5 via-primary/10 to-primary/5">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="mx-auto max-w-4xl text-center"
        >
          <h2 className="text-3xl font-bold tracking-tight sm:text-4xl mb-6">
            {t('cta.title')}
          </h2>
          <p className="text-lg leading-8 text-muted-foreground mb-12">
            {t('cta.subtitle')}
          </p>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
            {/* Email Card */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 bg-background">
                <CardContent className="p-8 text-center">
                  <div className="flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br from-primary/10 to-primary/20 mb-6 mx-auto">
                    <Mail className="h-8 w-8 text-primary" />
                  </div>
                  
                  <h3 className="text-xl font-semibold mb-3">
                    {t('cta.emailLabel')}
                  </h3>
                  
                  <p className="text-primary font-medium mb-6">
                    {t('cta.email')}
                  </p>
                  
                  <Button
                    onClick={handleEmailClick}
                    className="w-full bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 transition-all duration-300"
                  >
                    发送简历
                    <Send className="ml-2 h-4 w-4" />
                  </Button>
                </CardContent>
              </Card>
            </motion.div>

            {/* Phone Card */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 bg-background">
                <CardContent className="p-8 text-center">
                  <div className="flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br from-primary/10 to-primary/20 mb-6 mx-auto">
                    <Phone className="h-8 w-8 text-primary" />
                  </div>
                  
                  <h3 className="text-xl font-semibold mb-3">
                    {t('cta.phoneLabel')}
                  </h3>
                  
                  <p className="text-primary font-medium mb-6">
                    {t('cta.phone')}
                  </p>
                  
                  <Button
                    variant="outline"
                    onClick={handlePhoneClick}
                    className="w-full border-primary text-primary hover:bg-primary hover:text-white transition-all duration-300"
                  >
                    立即致电
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </CardContent>
              </Card>
            </motion.div>
          </div>

          {/* Additional contact info */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            viewport={{ once: true }}
            className="bg-background/50 backdrop-blur-sm rounded-2xl p-8 border"
          >
            <h3 className="text-lg font-semibold mb-4">
              其他联系方式
            </h3>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 text-sm">
              <div className="text-center">
                <div className="font-medium mb-1">工作时间</div>
                <div className="text-muted-foreground">周一至周五 9:00-18:00</div>
              </div>
              <div className="text-center">
                <div className="font-medium mb-1">公司地址</div>
                <div className="text-muted-foreground">北京市朝阳区科技园区</div>
              </div>
              <div className="text-center">
                <div className="font-medium mb-1">HR微信</div>
                <div className="text-muted-foreground">zerodots-hr</div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  )
}
