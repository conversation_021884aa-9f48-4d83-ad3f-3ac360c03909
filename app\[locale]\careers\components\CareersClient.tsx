"use client"

import { motion } from "framer-motion"
import { useTranslations, useRawTranslations } from '@/hooks/useTranslations'
import { CareersHero } from './CareersHero'
import { WhyJoinUs } from './WhyJoinUs'
import { OpenPositions } from './OpenPositions'
import { ApplicationProcess } from './ApplicationProcess'
import { CareersCTA } from './CareersCTA'

export default function CareersClient() {
  const t = useTranslations('careers')
  const rawData = useRawTranslations('careers')

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <CareersHero />
      
      {/* Why Join Us Section */}
      <WhyJoinUs />
      
      {/* Open Positions Section */}
      <OpenPositions />
      
      {/* Application Process Section */}
      <ApplicationProcess />
      
      {/* CTA Section */}
      <CareersCTA />
    </div>
  )
}
