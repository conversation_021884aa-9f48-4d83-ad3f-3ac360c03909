"use client"

import { motion } from "framer-motion"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  MapPin, Clock, Users, DollarSign, 
  ArrowRight, Briefcase 
} from "lucide-react"
import { useTranslations, useRawTranslations } from '@/hooks/useTranslations'
import { useState } from 'react'
import { JobDetailModal } from './JobDetailModal'

export function OpenPositions() {
  const t = useTranslations('careers')
  const rawData = useRawTranslations('careers')
  const [selectedJob, setSelectedJob] = useState(null)
  const [isModalOpen, setIsModalOpen] = useState(false)

  const positions = rawData.positions || []

  const handleViewDetails = (job: any) => {
    setSelectedJob(job)
    setIsModalOpen(true)
  }

  const handleApply = (job: any) => {
    // 这里可以实现申请逻辑，比如跳转到申请表单或发送邮件
    window.open(`mailto:<EMAIL>?subject=Application for ${job.title}&body=Dear HR Team,%0D%0A%0D%0AI am interested in applying for the ${job.title} position.%0D%0A%0D%0APlease find my resume attached.%0D%0A%0D%0ABest regards`)
  }

  return (
    <section id="open-positions" className="py-24 sm:py-32 bg-muted/30">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="mx-auto max-w-2xl text-center mb-16"
        >
          <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
            {t('openPositions.title')}
          </h2>
          <p className="mt-6 text-lg leading-8 text-muted-foreground">
            {t('openPositions.subtitle')}
          </p>
        </motion.div>

        {positions.length === 0 ? (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center py-12"
          >
            <Briefcase className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
            <p className="text-lg text-muted-foreground">
              {t('openPositions.noPositions')}
            </p>
          </motion.div>
        ) : (
          <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
            {positions.map((position: any, index: number) => (
              <motion.div
                key={position.id}
                initial={{ opacity: 0, y: 40 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="h-full border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 bg-background cursor-pointer group">
                  {/* 可点击的卡片内容区域 */}
                  <div onClick={() => handleViewDetails(position)} className="flex-1">
                    <CardHeader className="pb-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <CardTitle className="text-xl mb-2 group-hover:text-primary transition-colors duration-200">
                            {position.title}
                          </CardTitle>
                          <p className="text-sm text-muted-foreground mb-4 line-clamp-2">
                            {position.description}
                          </p>
                        </div>
                        <div className="ml-4 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                          <ArrowRight className="h-5 w-5 text-primary" />
                        </div>
                      </div>

                      <div className="flex flex-wrap gap-2">
                        {position.tags?.map((tag: string) => (
                          <Badge key={tag} variant="secondary" className="text-xs">
                            {tag}
                          </Badge>
                        ))}
                      </div>
                    </CardHeader>

                    <CardContent className="pt-0 pb-4">
                      <div className="grid grid-cols-2 gap-4 mb-6 text-sm">
                        <div className="flex items-center gap-2">
                          <MapPin className="h-4 w-4 text-muted-foreground" />
                          <span>{position.location}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4 text-muted-foreground" />
                          <span>{position.type}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Users className="h-4 w-4 text-muted-foreground" />
                          <span>{position.experience}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <DollarSign className="h-4 w-4 text-muted-foreground" />
                          <span>{position.salary}</span>
                        </div>
                      </div>
                    </CardContent>
                  </div>

                  {/* 按钮区域 */}
                  <CardContent className="pt-0 border-t border-muted/20">
                    <div className="flex gap-3">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleViewDetails(position)
                        }}
                        className="flex-1"
                      >
                        {t('openPositions.viewDetails')}
                      </Button>
                      <Button
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          handleApply(position)
                        }}
                        className="flex-1 bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70"
                      >
                        {t('openPositions.applyNow')}
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        )}
      </div>

      {/* Job Detail Modal */}
      <JobDetailModal
        job={selectedJob}
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onApply={handleApply}
      />
    </section>
  )
}
