"use client"

import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import { 
  Brain, Users, TrendingUp, Building, 
  Gift, Lightbulb 
} from "lucide-react"
import { useTranslations, useRawTranslations } from '@/hooks/useTranslations'

export function WhyJoinUs() {
  const t = useTranslations('careers')
  const rawData = useRawTranslations('careers')

  const iconMap = {
    brain: Brain,
    users: Users,
    'trending-up': TrendingUp,
    building: Building,
    gift: Gift,
    lightbulb: Lightbulb
  }

  const benefits = rawData.whyJoinUs?.benefits || []

  return (
    <section className="py-24 sm:py-32">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="mx-auto max-w-2xl text-center mb-16"
        >
          <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
            {t('whyJoinUs.title')}
          </h2>
          <p className="mt-6 text-lg leading-8 text-muted-foreground">
            {t('whyJoinUs.subtitle')}
          </p>
        </motion.div>

        <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
          {benefits.map((benefit: any, index: number) => {
            const IconComponent = iconMap[benefit.icon as keyof typeof iconMap] || Brain
            
            return (
              <motion.div
                key={benefit.title}
                initial={{ opacity: 0, y: 40 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="h-full border-0 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 bg-gradient-to-br from-background to-background/50 backdrop-blur-sm">
                  <CardContent className="p-8">
                    <div className="flex items-center justify-center w-16 h-16 rounded-2xl bg-gradient-to-br from-primary/10 to-primary/20 mb-6 mx-auto">
                      <IconComponent className="h-8 w-8 text-primary" />
                    </div>
                    
                    <h3 className="text-xl font-semibold text-center mb-4">
                      {benefit.title}
                    </h3>
                    
                    <p className="text-muted-foreground text-center leading-relaxed">
                      {benefit.description}
                    </p>
                  </CardContent>
                </Card>
              </motion.div>
            )
          })}
        </div>

        {/* Additional info section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          viewport={{ once: true }}
          className="mt-20 text-center"
        >
          <div className="bg-gradient-to-r from-primary/5 to-primary/10 rounded-3xl p-8 sm:p-12">
            <h3 className="text-2xl font-bold mb-4">
              更多福利待遇
            </h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 text-sm">
              <div className="flex items-center justify-center gap-2">
                <div className="w-2 h-2 bg-primary rounded-full"></div>
                <span>五险一金</span>
              </div>
              <div className="flex items-center justify-center gap-2">
                <div className="w-2 h-2 bg-primary rounded-full"></div>
                <span>年终奖金</span>
              </div>
              <div className="flex items-center justify-center gap-2">
                <div className="w-2 h-2 bg-primary rounded-full"></div>
                <span>带薪年假</span>
              </div>
              <div className="flex items-center justify-center gap-2">
                <div className="w-2 h-2 bg-primary rounded-full"></div>
                <span>健康体检</span>
              </div>
              <div className="flex items-center justify-center gap-2">
                <div className="w-2 h-2 bg-primary rounded-full"></div>
                <span>弹性工作</span>
              </div>
              <div className="flex items-center justify-center gap-2">
                <div className="w-2 h-2 bg-primary rounded-full"></div>
                <span>技能培训</span>
              </div>
              <div className="flex items-center justify-center gap-2">
                <div className="w-2 h-2 bg-primary rounded-full"></div>
                <span>团建活动</span>
              </div>
              <div className="flex items-center justify-center gap-2">
                <div className="w-2 h-2 bg-primary rounded-full"></div>
                <span>股权激励</span>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
