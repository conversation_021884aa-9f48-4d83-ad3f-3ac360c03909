import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { isValidLocale } from '@/lib/i18n'
import CareersClient from './components/CareersClient'

export async function generateMetadata({ params: { locale } }: { params: { locale: string } }): Promise<Metadata> {
  if (!isValidLocale(locale)) {
    notFound();
  }

  const isEnglish = locale === 'en';
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://zerodots.tech';

  return {
    title: isEnglish
      ? 'Careers - ZeroDots | Join Our Team of AI & Cloud Computing Experts'
      : '招聘信息 - 零点科技 | 加入我们的AI与云计算专家团队',
    description: isEnglish
      ? 'Join ZeroDots and work with top industry talents in AI and cloud computing. Explore exciting career opportunities and grow with us.'
      : '加入零点科技，与行业顶尖人才一起工作，在AI与云计算领域探索激动人心的职业机会，与我们共同成长。',
    keywords: isEnglish
      ? 'careers, jobs, AI engineer, cloud architect, frontend developer, product manager, ZeroDots'
      : '招聘, 工作机会, AI工程师, 云架构师, 前端开发, 产品经理, 零点科技',
    openGraph: {
      title: isEnglish
        ? 'Careers - ZeroDots | Join Our Team of AI & Cloud Computing Experts'
        : '招聘信息 - 零点科技 | 加入我们的AI与云计算专家团队',
      description: isEnglish
        ? 'Join ZeroDots and work with top industry talents in AI and cloud computing. Explore exciting career opportunities and grow with us.'
        : '加入零点科技，与行业顶尖人才一起工作，在AI与云计算领域探索激动人心的职业机会，与我们共同成长。',
      url: `${baseUrl}/${locale}/careers`,
      siteName: 'ZeroDots',
      images: [
        {
          url: `${baseUrl}/og-careers.jpg`,
          width: 1200,
          height: 630,
          alt: isEnglish ? 'ZeroDots Careers' : '零点科技招聘',
        },
      ],
      locale: isEnglish ? 'en_US' : 'zh_CN',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: isEnglish
        ? 'Careers - ZeroDots | Join Our Team of AI & Cloud Computing Experts'
        : '招聘信息 - 零点科技 | 加入我们的AI与云计算专家团队',
      description: isEnglish
        ? 'Join ZeroDots and work with top industry talents in AI and cloud computing.'
        : '加入零点科技，与行业顶尖人才一起工作，在AI与云计算领域发展。',
      images: [`${baseUrl}/og-careers.jpg`],
    },
    alternates: {
      canonical: `${baseUrl}/${locale}/careers`,
      languages: {
        'zh-CN': `${baseUrl}/careers`,
        'en-US': `${baseUrl}/en/careers`,
      },
    },
  };
}

export default function Careers({ params: { locale } }: { params: { locale: string } }) {
  if (!isValidLocale(locale)) {
    notFound();
  }

  return <CareersClient />;
}
