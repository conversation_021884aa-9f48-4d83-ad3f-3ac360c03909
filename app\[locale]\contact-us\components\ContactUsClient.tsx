"use client"

import { motion } from "framer-motion"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

import {
  Phone,
  Mail,
  MapPin,
  MessageSquare,
  Building2,
  Headphones,
  Users,
  Clock,
  Globe,
  Shield,
  Zap,
  ArrowRight,
  CheckCircle,
  HelpCircle,
  MessageCircle
} from "lucide-react"
import ContactUsForm from './contactUsFrom'
import { useTranslations } from "@/hooks/useTranslations"

export default function ContactUsClient() {
  const t = useTranslations('contactUs')
  const tCommon = useTranslations('common')

  const contactMethods = [
    {
      icon: Phone,
      title: t('contactMethods.customerService.title'),
      value: t('contactMethods.customerService.value'),
      desc: t('contactMethods.customerService.desc'),
      available: true,
      color: "from-blue-500 to-blue-600"
    },
    {
      icon: Mail,
      title: t('contactMethods.email.title'),
      value: t('contactMethods.email.value'),
      desc: t('contactMethods.email.desc'),
      available: true,
      color: "from-green-500 to-green-600"
    },
    {
      icon: MapPin,
      title: t('contactMethods.address.title'),
      value: t('contactMethods.address.value'),
      desc: t('contactMethods.address.desc'),
      available: true,
      color: "from-purple-500 to-purple-600"
    }
  ]

  const departments = [
    {
      icon: Headphones,
      title: t('departments.techSupport.title'),
      email: t('departments.techSupport.email'),
      desc: t('departments.techSupport.desc'),
      responseTime: t('departments.techSupport.responseTime'),
      color: "from-blue-500 to-cyan-500"
    },
    {
      icon: Building2,
      title: t('departments.business.title'),
      email: t('departments.business.email'),
      desc: t('departments.business.desc'),
      responseTime: t('departments.business.responseTime'),
      color: "from-green-500 to-emerald-500"
    },
    {
      icon: MessageSquare,
      title: t('departments.media.title'),
      email: t('departments.media.email'),
      desc: t('departments.media.desc'),
      responseTime: t('departments.media.responseTime'),
      color: "from-purple-500 to-violet-500"
    },
    {
      icon: Users,
      title: t('departments.hr.title'),
      email: t('departments.hr.email'),
      desc: t('departments.hr.desc'),
      responseTime: t('departments.hr.responseTime'),
      color: "from-orange-500 to-red-500"
    }
  ]

  const faqs = [
    {
      question: t('faq.questions.howToStart.question'),
      answer: t('faq.questions.howToStart.answer')
    },
    {
      question: t('faq.questions.responseTime.question'),
      answer: t('faq.questions.responseTime.answer')
    },
    {
      question: t('faq.questions.freeTrial.question'),
      answer: t('faq.questions.freeTrial.answer')
    },
    {
      question: t('faq.questions.dataSecurity.question'),
      answer: t('faq.questions.dataSecurity.answer')
    }
  ]

  const features = [
    {
      icon: Clock,
      title: t('hero.features.support247')
    },
    {
      icon: Globe,
      title: t('hero.features.globalService')
    },
    {
      icon: Shield,
      title: t('hero.features.securityGuarantee')
    },
    {
      icon: Zap,
      title: t('hero.features.quickResponse')
    }
  ]
  
  return (
    <div className="relative isolate min-h-screen">
      {/* 增强的背景效果 */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-primary/3 to-transparent" />
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-primary/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-blue-400/10 rounded-full blur-3xl animate-pulse delay-1000" />
        <div className="grid-bg absolute inset-0 opacity-30" />
      </div>

      {/* Hero Section */}
      <section className="relative py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            className="mx-auto max-w-3xl text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="mb-6"
            >
              <Badge variant="outline" className="mb-4 px-4 py-2 text-sm font-medium border-primary/20 bg-primary/5">
                <MessageCircle className="w-4 h-4 mr-2" />
                {t('hero.badge')}
              </Badge>
            </motion.div>

            <motion.h1
              className="text-4xl font-bold tracking-tight sm:text-6xl lg:text-7xl mb-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <span className="text-gradient-modern">{t('hero.title')}</span>
            </motion.h1>

            <motion.p
              className="text-xl leading-8 text-muted-foreground mb-8 max-w-2xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              {t('hero.subtitle')}
            </motion.p>

            <motion.div
              className="flex flex-wrap justify-center gap-4 mb-12"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
            >
              {features.map((feature, index) => (
                <div key={feature.title} className="flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full border border-white/20">
                  <feature.icon className="w-4 h-4 text-primary" />
                  <span className="text-sm font-medium">{feature.title}</span>
                </div>
              ))}
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* 联系方式卡片 */}
      <section className="py-16">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold mb-4">{t('contactMethods.title')}</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              {t('contactMethods.subtitle')}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
            {contactMethods.map((method, index) => (
              <motion.div
                key={method.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -5 }}
                className="group"
              >
                <Card className="relative overflow-hidden border-0 bg-white/60 backdrop-blur-xl shadow-lg hover:shadow-xl transition-all duration-500 h-full">
                  <div className={`absolute inset-0 bg-gradient-to-br ${method.color} opacity-0 group-hover:opacity-5 transition-opacity duration-500`} />
                  <CardContent className="p-8 relative">
                    <div className="flex items-start gap-4 mb-6">
                      <div className={`p-4 rounded-2xl bg-gradient-to-br ${method.color} shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                        <method.icon className="h-6 w-6 text-white" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="text-xl font-semibold group-hover:text-primary transition-colors">
                            {method.title}
                          </h3>
                          {method.available && (
                            <Badge variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200">
                              <CheckCircle className="w-3 h-3 mr-1" />
                              {t('contactMethods.customerService.online')}
                            </Badge>
                          )}
                        </div>
                        <p className="text-lg font-medium mb-2 text-foreground">{method.value}</p>
                        <p className="text-sm text-muted-foreground mb-4">{method.desc}</p>
                      </div>
                    </div>

                    <Button
                      variant="outline"
                      className="w-full group-hover:bg-primary group-hover:text-primary-foreground transition-all duration-300"
                    >
                      {t('contactMethods.customerService.contactNow')}
                      <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                    </Button>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* 联系表单 */}
      <ContactUsForm />

      {/* 部门联系方式 */}
      <section className="py-16 bg-muted/30">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold mb-4">{t('departments.title')}</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              {t('departments.subtitle')}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
            {departments.map((dept, index) => (
              <motion.div
                key={dept.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -5 }}
                className="group"
              >
                <Card className="relative overflow-hidden border-0 bg-white/60 backdrop-blur-xl shadow-lg hover:shadow-xl transition-all duration-500 h-full">
                  <div className={`absolute inset-0 bg-gradient-to-br ${dept.color} opacity-0 group-hover:opacity-5 transition-opacity duration-500`} />
                  <CardContent className="p-6 relative">
                    <div className="text-center">
                      <div className={`inline-flex p-4 rounded-2xl bg-gradient-to-br ${dept.color} shadow-lg group-hover:scale-110 transition-transform duration-300 mb-4`}>
                        <dept.icon className="h-6 w-6 text-white" />
                      </div>
                      <h3 className="text-lg font-semibold mb-2 group-hover:text-primary transition-colors">
                        {dept.title}
                      </h3>
                      <p className="text-sm font-medium text-primary mb-2">{dept.email}</p>
                      <p className="text-sm text-muted-foreground mb-4">{dept.desc}</p>
                      <div className="flex items-center justify-center gap-2 text-xs text-muted-foreground">
                        <Clock className="w-3 h-3" />
                        <span>{t('departments.responseTimeLabel')} {dept.responseTime}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16">
        <div className="mx-auto max-w-4xl px-6 lg:px-8">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold mb-4">{t('faq.title')}</h2>
            <p className="text-muted-foreground">
              {t('faq.subtitle')}
            </p>
          </motion.div>

          <div className="space-y-6">
            {faqs.map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <Card className="border-0 bg-white/60 backdrop-blur-xl shadow-lg hover:shadow-xl transition-all duration-300">
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                      <div className="p-2 rounded-lg bg-primary/10 mt-1">
                        <HelpCircle className="h-5 w-5 text-primary" />
                      </div>
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold mb-2">{faq.question}</h3>
                        <p className="text-muted-foreground">{faq.answer}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-br from-primary/5 to-primary/10">
        <div className="mx-auto max-w-4xl px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold mb-4">{t('cta.title')}</h2>
            <p className="text-muted-foreground mb-8 max-w-2xl mx-auto">
              {t('cta.subtitle')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70">
                <MessageCircle className="w-5 h-5 mr-2" />
                {t('cta.onlineService')}
              </Button>
              <Button variant="outline" size="lg">
                <Phone className="w-5 h-5 mr-2" />
                {t('cta.phoneConsultation')}
              </Button>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  )
}
