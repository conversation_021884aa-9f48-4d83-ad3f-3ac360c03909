'use client'

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Loader2, Send } from "lucide-react";
import { useContactForm } from "./useContactForm";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { useTranslations } from "@/hooks/useTranslations";

export default function ContactUsForm() {
  const t = useTranslations('contactUs.form')
  const {
    formData,
    isSubmitting,
    errors,
    handleSubmit,
    handleInputChange,
    handleCheckboxChange
  } = useContactForm()

  const formFields = [
    {
      name: "name",
      label: t('name'),
      type: "text",
      placeholder: t('namePlaceholder'),
      required: true,
    },
    {
      name: "email",
      label: t('email'),
      type: "email",
      placeholder: t('emailPlaceholder'),
      required: true,
    },
    {
      name: "subject",
      label: t('subject'),
      type: "text",
      placeholder: t('subjectPlaceholder'),
      required: false,
    },
  ]

  return (
    <motion.div 
      className="mx-auto max-w-2xl mt-20"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.3 }}
    >
      <Card className="border-0 bg-gradient-to-br from-muted/50 to-muted/30 backdrop-blur-sm hover:shadow-lg transition-all duration-300">
        <CardContent className="p-6">
          <motion.h2
            className="text-xl font-semibold mb-6 text-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
          >
            {t('title')}
          </motion.h2>
          <form 
            className="space-y-6" 
            onSubmit={handleSubmit}
            noValidate
          >
            <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
              {formFields.slice(0, 2).map((field) => (
                <div key={field.name}>
                  <label 
                    htmlFor={field.name}
                    className="text-sm font-medium mb-2 block"
                  >
                    {field.label}
                    {field.required && <span className="text-red-500 ml-1">{t('required')}</span>}
                  </label>
                  <Input 
                    id={field.name}
                    name={field.name}
                    type={field.type}
                    value={formData[field.name as keyof typeof formData] as string}
                    onChange={handleInputChange}
                    placeholder={field.placeholder}
                    required={field.required}
                    className={cn(
                      "transition-all duration-200",
                      errors[field.name] && "border-red-500 focus-visible:ring-red-500"
                    )}
                    aria-invalid={errors[field.name] ? "true" : "false"}
                    aria-describedby={errors[field.name] ? `${field.name}-error` : undefined}
                  />
                  {errors[field.name] && (
                    <p 
                      id={`${field.name}-error`}
                      className="text-sm text-red-500 mt-1"
                    >
                      {errors[field.name]}
                    </p>
                  )}
                </div>
              ))}
            </div>
            
            <div>
              <label
                htmlFor="subject"
                className="text-sm font-medium mb-2 block"
              >
                {t('subject')}
              </label>
              <Input
                id="subject"
                name="subject"
                value={formData.subject}
                onChange={handleInputChange}
                placeholder={t('subjectPlaceholder')}
              />
            </div>

            <div>
              <label
                htmlFor="message"
                className="text-sm font-medium mb-2 block"
              >
                {t('message')}
                <span className="text-red-500 ml-1">{t('required')}</span>
              </label>
              <Textarea
                id="message"
                name="message"
                value={formData.message}
                onChange={handleInputChange}
                placeholder={t('messagePlaceholder')}
                className={cn(
                  "min-h-[120px]",
                  errors.message && "border-red-500 focus-visible:ring-red-500"
                )}
                required
                aria-invalid={errors.message ? "true" : "false"}
                aria-describedby={errors.message ? "message-error" : undefined}
              />
              {errors.message && (
                <p
                  id="message-error"
                  className="text-sm text-red-500 mt-1"
                >
                  {errors.message}
                </p>
              )}
            </div>

            <motion.div 
              className="space-y-4"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5 }}
            >
              <div className="flex items-start space-x-2">
                <Checkbox 
                  id="marketing" 
                  checked={formData.marketing}
                  onCheckedChange={handleCheckboxChange}
                  className="mt-1" 
                />
                <div className="grid gap-1.5 leading-none">
                  <label
                    htmlFor="marketing"
                    className="text-sm text-muted-foreground cursor-pointer"
                  >
                    {t('marketing')}
                  </label>
                </div>
              </div>
              <p className="text-xs text-muted-foreground">
                {t('privacyNotice')}
                <a
                  href="/privacy"
                  className="underline hover:text-primary ml-1 transition-colors"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  {t('privacyPolicy')}
                </a>
              </p>
            </motion.div>

            <Button 
              type="submit"
              className="w-full bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 group disabled:opacity-50"
              disabled={isSubmitting || Object.keys(errors).length > 0}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {t('sending')}
                </>
              ) : (
                <>
                  {t('submit')}
                  <Send className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                </>
              )}
            </Button>
          </form>
        </CardContent>
      </Card>
    </motion.div>
  )
}