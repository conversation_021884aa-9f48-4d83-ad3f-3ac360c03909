import '../globals.css';
import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import { ThemeProvider } from '@/components/theme-provider';
import { Toaster } from 'sonner'
import { StructuredData } from '@/components/StructuredData';
import { TranslationProvider } from '@/hooks/useTranslations';
import { getMessages, isValidLocale, defaultLocale, type Locale } from '@/lib/i18n';
import { notFound } from 'next/navigation';

const inter = Inter({ 
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter',
});

const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://zerodots.tech';

export const metadata: Metadata = {
  metadataBase: new URL(baseUrl),
  title: {
    default: '零点科技 - 企业级AI与云计算解决方案提供商',
    template: '%s | 零点科技',
  },
  description: '零点科技专注于AI智能标注、CPU算力租用、教育培训管理和定制软件开发，为企业提供专业的技术解决方案和服务。成立于2025年，致力于推动企业数字化转型。',
  keywords: [
    '零点科技',
    'ZeroDots',
    'AI智能标注',
    'CPU算力租用',
    '教育培训管理',
    '定制软件开发',
    '企业数字化',
    '云计算服务',
    '人工智能',
    '数据标注',
    '高性能计算'
  ],
  authors: [{ name: '零点科技' }],
  creator: '零点科技',
  publisher: '零点科技',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  openGraph: {
    type: 'website',
    locale: 'zh_CN',
    url: baseUrl,
    siteName: '零点科技',
    title: '零点科技 - 企业级AI与云计算解决方案提供商',
    description: '零点科技专注于AI智能标注、CPU算力租用、教育培训管理和定制软件开发，为企业提供专业的技术解决方案和服务。',
    images: [
      {
        url: `${baseUrl}/og-image.jpg`,
        width: 1200,
        height: 630,
        alt: '零点科技 - 企业级AI与云计算解决方案',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: '零点科技 - 企业级AI与云计算解决方案提供商',
    description: '零点科技专注于AI智能标注、CPU算力租用、教育培训管理和定制软件开发',
    images: [`${baseUrl}/og-image.jpg`],
    creator: '@zerodots_tech',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
    yandex: 'your-yandex-verification-code',
    yahoo: 'your-yahoo-verification-code',
  },
  alternates: {
    canonical: baseUrl,
    languages: {
      'zh-CN': baseUrl,
      'en-US': `${baseUrl}/en`,
    },
  },
};

export async function generateStaticParams() {
  return [
    { locale: 'zh' },
    { locale: 'en' }
  ];
}

export default async function LocaleLayout({
  children,
  params: { locale }
}: {
  children: React.ReactNode;
  params: { locale: string };
}) {
  // Validate locale
  if (!isValidLocale(locale)) {
    notFound();
  }

  // Get messages for the current locale
  const messages = await getMessages(locale as Locale);

  return (
    <html lang={locale} suppressHydrationWarning>
      <head>
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="icon" href="/icon.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />
        <meta name="theme-color" content="#3b82f6" />
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5" />
      </head>
      <body className={`${inter.variable} font-sans antialiased`}>
        <TranslationProvider locale={locale as Locale} messages={messages}>
          <StructuredData />
          <ThemeProvider
            attribute="class"
            defaultTheme="system"
            enableSystem
            disableTransitionOnChange
          >
            <div className="flex min-h-screen flex-col">
              <Navbar />
              <main className="flex-1" role="main">
                {children}
              </main>
              <Footer />
            </div>
          </ThemeProvider>
          <Toaster richColors position="top-center" />
        </TranslationProvider>
      </body>
    </html>
  );
}
