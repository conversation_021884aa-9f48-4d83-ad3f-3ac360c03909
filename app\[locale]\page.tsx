import { Metadata } from 'next'
import StatsSection from "@/components/home/<USER>"
import HeroSection from "@/components/home/<USER>"
import FeaturesSection from "@/components/home/<USER>"
import SolutionsSection from "@/components/home/<USER>"
import TestimonialsSection from "@/components/home/<USER>"
import PartnersSection from "@/components/home/<USER>"
import CTASection from "@/components/home/<USER>"
import ServicesOverview from "@/components/home/<USER>"
import TechnologyStack from "@/components/home/<USER>"
import ProcessFlow from "@/components/home/<USER>"
import ClientShowcase from "@/components/home/<USER>"
import { isValidLocale } from '@/lib/i18n'
import { notFound } from 'next/navigation'

const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://zerodots.tech';

// Dynamic metadata based on locale
export async function generateMetadata({ params: { locale } }: { params: { locale: string } }): Promise<Metadata> {
  const isEnglish = locale === 'en';

  return {
    title: isEnglish
      ? 'zerodots - Enterprise AI & Cloud Computing Solutions Provider'
      : '零点科技 - 企业级AI与云计算解决方案提供商',
    description: isEnglish
      ? 'zerodots specializes in AI annotation, CPU rental, education management, and custom software development, providing professional technical solutions and services for enterprises.'
      : '零点科技专注于AI智能标注、CPU算力租用、教育培训管理和定制软件开发，为企业提供专业的技术解决方案和服务。',
    openGraph: {
      title: isEnglish
        ? 'zerodots - Enterprise AI & Cloud Computing Solutions Provider'
        : '零点科技 - 企业级AI与云计算解决方案提供商',
      description: isEnglish
        ? 'zerodots specializes in AI annotation, CPU rental, education management, and custom software development'
        : '零点科技专注于AI智能标注、CPU算力租用、教育培训管理和定制软件开发',
      url: baseUrl,
      siteName: isEnglish ? 'zerodots' : '零点科技',
      images: [
        {
          url: `${baseUrl}/og-home.jpg`,
          width: 1200,
          height: 630,
          alt: isEnglish ? 'zerodots Homepage' : '零点科技首页',
        },
      ],
      locale: isEnglish ? 'en_US' : 'zh_CN',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: isEnglish
        ? 'zerodots - Enterprise AI & Cloud Computing Solutions Provider'
        : '零点科技 - 企业级AI与云计算解决方案提供商',
      description: isEnglish
        ? 'zerodots specializes in AI annotation, CPU rental, education management, and custom software development'
        : '零点科技专注于AI智能标注、CPU算力租用、教育培训管理和定制软件开发',
      images: [`${baseUrl}/og-home.jpg`],
    },
    alternates: {
      canonical: baseUrl,
    },
  };
}

export default function Home({ params: { locale } }: { params: { locale: string } }) {
  // Validate locale
  if (!isValidLocale(locale)) {
    notFound();
  }

  const isEnglish = locale === 'en';

  return (
    <>
      <div className="relative overflow-hidden">
        {/* Hero Section - 主视觉区域 */}
        <HeroSection />

        {/* Services Overview - 业务概览 */}
        <ServicesOverview />

        {/* Stats Section - 数据统计 */}
        <StatsSection />

        {/* Features Section - 核心功能 */}
        <FeaturesSection />

        {/* Process Flow - 业务流程 */}
        <ProcessFlow />

        {/* Technology Stack - 技术栈 */}
        {/* <TechnologyStack /> */}

        {/* Solutions Section - 解决方案 */}
        <SolutionsSection />

        {/* Client Showcase - 客户案例 */}
        <ClientShowcase />

        {/* Testimonials Section - 客户评价 */}
        <TestimonialsSection />

        {/* Partners Section - 合作伙伴 */}
        <PartnersSection />

        {/* CTA Section - 行动召唤 */}
        <CTASection />
      </div>

      {/* 首页结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebPage",
            "name": isEnglish ? "zerodots Homepage" : "零点科技首页",
            "description": isEnglish
              ? "zerodots specializes in AI annotation, CPU rental, education management, and custom software development"
              : "零点科技专注于AI智能标注、CPU算力租用、教育培训管理和定制软件开发",
            "url": baseUrl,
            "mainEntity": {
              "@type": "Organization",
              "name": isEnglish ? "zerodots" : "零点科技",
              "description": isEnglish
                ? "Enterprise AI & Cloud Computing Solutions Provider"
                : "企业级AI与云计算解决方案提供商"
            },
            "breadcrumb": {
              "@type": "BreadcrumbList",
              "itemListElement": [
                {
                  "@type": "ListItem",
                  "position": 1,
                  "name": isEnglish ? "Home" : "首页",
                  "item": baseUrl
                }
              ]
            }
          }),
        }}
      />
    </>
  )
}
