import { Metadata } from 'next'

export const metadata: Metadata = {
  title: '隐私政策 | zerodots',
  description: 'zerodots的隐私政策说明',
}

const privacyContent = {
  introduction: "本隐私政策说明了零点科技（以下简称“我们”）如何收集、使用和保护您的个人信息。我们深知个人信息对您的重要性，并会尽全力保护您的个人信息安全。",
  sections: [
    {
      title: "信息收集和使用",
      content: [
        "我们收集的信息包括但不限于：",
        "• 基本信息：姓名、联系方式、公司信息等",
        "• 设备信息：IP地址、浏览器类型、操作系统等",
        "• 使用数据：访问记录、使用情况统计等",
        "• 交易信息：订单记录、支付信息等",
        "这些信息将用于：",
        "• 提供和改进我们的产品和服务",
        "• 处理您的订单和请求",
        "• 发送服务通知和更新信息",
        "• 提供客户支持和技术支持"
      ]
    },
    {
      title: "Cookie使用",
      content: [
        "我们使用Cookie和类似技术来：",
        "• 记住您的偏好设置",
        "• 分析网站流量和使用情况",
        "• 优化用户体验",
        "• 提供个性化服务",
        "您可以通过浏览器设置管理Cookie偏好，但这可能会影响某些功能的使用。"
      ]
    },
    {
      title: "数据安全",
      content: [
        "我们采取多重措施保护您的数据：",
        "• 采用行业标准的加密技术",
        "• 实施严格的访问控制",
        "• 定期安全审计和评估",
        "• 员工保密培训",
        "• 数据备份和灾难恢复机制"
      ]
    },
    {
      title: "您的权利",
      content: [
        "根据适用的数据保护法律，您享有以下权利：",
        "• 访问：获取我们持有的您的个人信息副本",
        "• 更正：要求更正不准确的个人信息",
        "• 删除：要求删除不再需要的个人信息",
        "• 限制：限制我们处理您的个人信息的方式",
        "• 反对：反对我们处理您的个人信息",
        "• 数据迁移：获取您的个人信息的电子副本"
      ]
    },
    {
      title: "信息共享",
      content: [
        "我们不会出售您的个人信息。仅在以下情况下可能共享您的信息：",
        "• 经您明确同意",
        "• 与我们的服务提供商合作",
        "• 遵守法律法规要求",
        "• 保护我们的合法权益"
      ]
    },
    {
      title: "政策更新",
      content: [
        "我们可能会定期更新本隐私政策：",
        "• 重大变更将通过电子邮件通知",
        "• 继续使用我们的服务即表示接受更新后的政策",
        "• 建议定期查看本页面了解最新政策"
      ]
    },
    {
      title: "联系我们",
      content: [
        "如有任何问题或疑虑，请通过以下方式联系我们：",
        "• 邮箱：<EMAIL>",
        "• 电话：400-888-8888",
        "• 地址：北京市朝阳区科技园区888号",
        "我们承诺在收到您的请求后的15个工作日内回复。"
      ]
    }
  ],
  lastUpdated: "2025年5月1日"
}

export default function PrivacyPage() {
  return (
    <div className="py-24 sm:py-32">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <h1 className="mb-8 text-4xl font-bold tracking-tight bg-gradient-to-r from-primary to-primary/50 bg-clip-text text-transparent">
          隐私政策
        </h1>
        
        <div className="prose prose-slate dark:prose-invert max-w-none">
          <p className="text-lg text-muted-foreground mb-12">
            {privacyContent.introduction}
          </p>
          
          <div className="space-y-12">
            {privacyContent.sections.map((section) => (
              <section key={section.title} className="group">
                <h2 className="text-2xl font-semibold mb-6 group-hover:text-primary transition-colors">
                  {section.title}
                </h2>
                <div className="space-y-4">
                  {section.content.map((paragraph, index) => (
                    <p key={index} className="text-muted-foreground leading-relaxed">
                      {paragraph}
                    </p>
                  ))}
                </div>
              </section>
            ))}
          </div>
          
          <div className="mt-12 pt-8 border-t border-muted">
            <p className="text-sm text-muted-foreground">
              最后更新时间：{privacyContent.lastUpdated}
            </p>
          </div>
        </div>
      </div>
    </div>
  )
} 