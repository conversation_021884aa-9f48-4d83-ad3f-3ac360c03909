'use client'

import { motion } from "framer-motion"
import { <PERSON><PERSON>les, Zap, Target, ArrowRight, Star, TrendingUp, Shield } from "lucide-react"
import { useTranslations } from '@/hooks/useTranslations'

export function ProductHero() {
  const t = useTranslations('productsPage')
  const tCommon = useTranslations('common')

  return (
    <section className="relative py-20 sm:py-28 overflow-hidden">
      {/* 增强的背景层 */}
      <div className="absolute inset-0 -z-10">
        {/* 主背景渐变 */}
        <div className="absolute inset-0 bg-gradient-to-br from-background via-primary/5 to-primary/10 dark:from-background dark:via-primary/10 dark:to-primary/20" />

        {/* 动态光效 */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-1/4 w-96 h-96 bg-primary/10 dark:bg-primary/20 rounded-full blur-3xl animate-pulse" />
          <div className="absolute bottom-0 right-1/4 w-80 h-80 bg-primary/8 dark:bg-primary/15 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '2s' }} />
        </div>

        {/* 网格背景 */}
        <div className="absolute inset-0 opacity-30">
          <div className="absolute inset-0" style={{
            backgroundImage: `radial-gradient(circle at 1px 1px, hsl(var(--primary) / 0.15) 1px, transparent 0)`,
            filter: 'opacity(0.8)',
            backgroundSize: '40px 40px'
          }} />
        </div>

        {/* 渐变遮罩 */}
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-background/20 to-background/40 dark:via-background/10 dark:to-background/20" />
      </div>

      {/* 浮动装饰元素 */}
      <div className="absolute top-20 left-10 w-72 h-72 rounded-full blur-3xl animate-float opacity-30 bg-primary/8 dark:bg-primary/15" />
      <div className="absolute bottom-20 right-10 w-96 h-96 rounded-full blur-3xl animate-float opacity-25 bg-primary/6 dark:bg-primary/12" style={{ animationDelay: '3s' }} />
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] rounded-full blur-3xl animate-float opacity-20 bg-primary/5 dark:bg-primary/10" style={{ animationDelay: '1.5s' }} />

      <div className="relative mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-5xl text-center">
          {/* 增强的标题区域 */}
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, ease: "easeOut" }}
          >
            {/* 标签徽章 */}
            <motion.div
              className="inline-flex items-center gap-2 px-5 py-2.5 rounded-full backdrop-blur-md border mb-8 shadow-lg bg-primary/10 dark:bg-primary/20 border-primary/30 dark:border-primary/40 shadow-primary/15"
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", stiffness: 400, damping: 10 }}
            >
              <Sparkles className="w-4 h-4 text-primary" />
              <span className="text-sm font-semibold text-primary">{t('heroTag')}</span>
              <ArrowRight className="w-3 h-3 ml-1 text-primary" />
            </motion.div>

            {/* 主标题 */}
            <motion.h1
              className="text-5xl font-bold tracking-tight sm:text-6xl lg:text-7xl xl:text-8xl mb-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, delay: 0.2 }}
            >
              <span className="block text-gradient-modern">
                {t('title')}
              </span>
            </motion.h1>

            {/* 副标题 */}
            <motion.p
              className="text-xl sm:text-2xl leading-relaxed text-muted-foreground max-w-4xl mx-auto mb-12"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, delay: 0.4 }}
            >
              {t('subtitle')}
            </motion.p>

            {/* 统计数据 */}
            <motion.div
              className="flex flex-wrap justify-center gap-8 mb-12"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 1, delay: 0.6 }}
            >
              {[
                { icon: Star, number: "50+", label: t('stats.qualityProducts') },
                { icon: TrendingUp, number: "99%", label: t('stats.satisfaction') },
                { icon: Shield, number: "24/7", label: t('stats.support') }
              ].map((stat, index) => (
                <motion.div
                  key={index}
                  className="flex items-center gap-3 px-4 py-2 rounded-xl bg-white/60 backdrop-blur-sm border border-white/40"
                  whileHover={{ scale: 1.05, y: -2 }}
                  transition={{ type: "spring", stiffness: 400, damping: 10 }}
                >
                  <div className="p-2 rounded-lg bg-primary/10 dark:bg-primary/20">
                    <stat.icon className="w-4 h-4 text-primary" />
                  </div>
                  <div className="text-left">
                    <div className="text-lg font-bold text-foreground">{stat.number}</div>
                    <div className="text-xs text-muted-foreground">{stat.label}</div>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </motion.div>

          {/* 重新设计的特色亮点 */}
          <motion.div
            className="grid grid-cols-1 md:grid-cols-3 gap-6 lg:gap-8 max-w-5xl mx-auto"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 1.0 }}
          >
            {[
              {
                icon: Target,
                title: t('features.precision'),
                description: t('features.precisionDesc'),
                gradient: 'from-primary to-primary/80',
                bgGradient: 'from-primary/5 to-primary/10'
              },
              {
                icon: Zap,
                title: t('features.deployment'),
                description: t('features.deploymentDesc'),
                gradient: 'from-primary to-primary/80',
                bgGradient: 'from-primary/5 to-primary/10'
              },
              {
                icon: Sparkles,
                title: t('features.innovation'),
                description: t('features.innovationDesc'),
                gradient: 'from-primary to-primary/80',
                bgGradient: 'from-primary/5 to-primary/10'
              }
            ].map((item, index) => (
              <motion.div
                key={index}
                className="group relative"
                initial={{ opacity: 0, y: 20, scale: 0.9 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{
                  duration: 0.6,
                  delay: 1.2 + index * 0.1,
                  type: "spring",
                  stiffness: 100
                }}
                whileHover={{ y: -8, scale: 1.02 }}
              >
                {/* 卡片主体 */}
                <div className={`relative flex flex-col items-center text-center p-8 rounded-3xl bg-gradient-to-br ${item.bgGradient} dark:from-background/20 dark:to-background/10 border border-border/60 backdrop-blur-sm shadow-lg group-hover:shadow-2xl transition-all duration-500 overflow-hidden`}>

                  {/* 背景装饰 */}
                  <div className="absolute inset-0 bg-gradient-to-br from-background/40 to-background/10 dark:from-background/20 dark:to-background/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                  <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-background/20 to-transparent dark:from-background/10 rounded-full blur-2xl" />

                  {/* 图标容器 */}
                  <motion.div
                    className={`relative p-4 rounded-2xl mb-6 bg-gradient-to-br ${item.gradient} shadow-lg group-hover:shadow-xl transition-all duration-500`}
                    whileHover={{ rotate: [0, -5, 5, 0], scale: 1.1 }}
                    transition={{ duration: 0.5 }}
                  >
                    <item.icon className="w-8 h-8 text-white relative z-10" />

                    {/* 图标光晕 */}
                    <div className={`absolute inset-0 rounded-2xl bg-gradient-to-br ${item.gradient} opacity-50 blur-lg group-hover:opacity-75 transition-opacity duration-500`} />
                  </motion.div>

                  {/* 内容 */}
                  <div className="relative z-10">
                    <h3 className="text-xl font-bold text-foreground mb-3 transition-colors duration-300">
                      {item.title}
                    </h3>
                    <p className="text-muted-foreground leading-relaxed transition-colors duration-300">
                      {item.description}
                    </p>
                  </div>

                  {/* 悬停时的边框光效 */}
                  <div className={`absolute inset-0 rounded-3xl bg-gradient-to-br ${item.gradient} opacity-0 group-hover:opacity-20 transition-opacity duration-500 -z-10 blur-xl`} />
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </div>
    </section>
  )
}
