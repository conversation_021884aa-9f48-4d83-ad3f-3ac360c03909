"use client"

import { ProductCardCompact } from "./ProductCardCompact"
import { ProductHero } from "./ProductHero"
import { ProductFeatures } from "./ProductFeatures"
import { ProductStats } from "./ProductStats"
import { ProductCTA } from "./ProductCTA"
import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { useTranslations } from '@/hooks/useTranslations'

interface Product {
  slug: string
  name: string
  description: string
  iconName: string
  features: Array<{ text: string; iconName: string }>
  highlight?: string
  price?: string
  category: string
}

interface ProductsClientProps {
  products: Product[]
}

export function ProductsClient({ products }: ProductsClientProps) {
  const t = useTranslations('productsPage')

  return (
    <div className="relative isolate">
      {/* Hero Section */}
      <ProductHero />

      {/* Direct Product Grid - No Categories */}
      <section className="py-20 sm:py-24 bg-gradient-to-br from-background/20 via-background to-primary/5 dark:from-background/10 dark:via-background dark:to-primary/10 relative overflow-hidden">
        {/* Background decorative elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-40 left-20 w-40 h-40 bg-primary/10 dark:bg-primary/20 rounded-full blur-3xl animate-pulse" />
          <div className="absolute bottom-40 right-20 w-32 h-32 bg-primary/8 dark:bg-primary/15 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '3s' }} />
          <div className="absolute top-1/2 left-1/4 w-24 h-24 bg-primary/5 dark:bg-primary/10 rounded-full blur-2xl animate-pulse" style={{ animationDelay: '6s' }} />
        </div>

        <div className="relative mx-auto max-w-7xl px-6 lg:px-8">
          {/* Enhanced Section Header */}
          <motion.div
            className="text-center mb-16"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <motion.div
              className="inline-flex items-center gap-3 px-6 py-3 rounded-full bg-gradient-to-r from-primary/10 to-primary/15 dark:from-primary/20 dark:to-primary/25 border border-primary/20 backdrop-blur-sm mb-6 shadow-sm"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.2 }}
            >
              <motion.div
                className="w-3 h-3 rounded-full bg-primary"
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              />
              <span className="text-sm font-semibold text-primary">
                {t('coreProducts')}
              </span>
            </motion.div>
            <h3 className="text-3xl sm:text-4xl font-bold text-foreground mb-4">
              {t('professionalSolutions')}
            </h3>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto">
              {t('coreProductsDescription', { count: products.length })}
            </p>
          </motion.div>

          <motion.div
            className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-2 xl:grid-cols-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            {products.map((product, index) => (
              <motion.div
                key={product.slug}
                initial={{ opacity: 0, y: 40, scale: 0.9 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{
                  duration: 0.6,
                  delay: index * 0.2,
                  type: "spring",
                  stiffness: 120,
                  damping: 12
                }}
                className="h-full"
              >
                <ProductCardCompact product={product} index={index} />
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Product Features */}
      <ProductFeatures />

      {/* Product Stats */}
      <ProductStats />

      {/* CTA Section */}
      <ProductCTA />
    </div>
  )
}
