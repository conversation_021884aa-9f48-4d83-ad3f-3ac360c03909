'use client'

import { motion } from "framer-motion"
import { <PERSON>, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { useTranslations, useRawTranslations } from "@/hooks/useTranslations"
import {
  ArrowRight,
  CheckCircle,
  Star,
  Shield,
  Zap,
  Globe,
  Users,
  Cpu,
  Database,
  BarChart,
  MessageCircle,
  Calendar,
  Download,
  Server,
  Activity,
  Award,
  Clock,
  DollarSign,
  TrendingUp,
  Monitor,
  HardDrive,
  Network,
  Brain,
  Layers,
  Code,
  Palette,
  Link as LinkIcon,
  Eye,
  AlertTriangle,
  BarChart3,
  FileText,
  Settings
} from "lucide-react"
import Link from "next/link"

interface ProductDetail {
  name: string
  description: string
  features: string[]
  techSpecs: {
    deployment: string
    security: string
    availability: string
    support: string
  }
  featureList?: Array<{
    title: string
    description: string
    features: Array<{
      name: string
      description: string
      icon: string
    }>
  }>
  demoVideo?: {
    url: string
    thumbnail: string
  }
  benefits?: Array<{
    title: string
    description: string
  }>
}

interface Props {
  product: ProductDetail
  productSlug: string
}

const iconMap = {
  Cpu,
  Database,
  BarChart,
  Shield,
  Zap,
  Globe,
  Users,
  CheckCircle,
  Star,
  Server,
  Activity,
  Award,
  Clock,
  DollarSign,
  TrendingUp,
  Monitor,
  HardDrive,
  Network,
  Brain,
  Layers,
  Code,
  Palette,
  LinkIcon,
  Eye,
  AlertTriangle,
  BarChart3,
  FileText,
  Settings
}

export function ProductDetailClient({ product, productSlug }: Props) {
  const t = useTranslations('productDetails.cpuRental')
  const tCommon = useTranslations('common')
  const rawData = useRawTranslations('cpuRental')

  const renderIcon = (iconName: string, className?: string) => {
    const IconComponent = iconMap[iconName as keyof typeof iconMap]
    return IconComponent ? <IconComponent className={className} /> : <Cpu className={className} />
  }

  // Helper function to get monitoring feature titles
  const getMonitoringTitle = (key: string): string => {
    const titles: Record<string, string> = {
      'performanceMonitoring': rawData.monitoring?.features?.performanceMonitoring?.split('、')[0] ||
                              rawData.monitoring?.features?.performanceMonitoring?.split(',')[0] ||
                              'Performance Monitoring',
      'alertSystem': rawData.monitoring?.features?.alertSystem || 'Alert System',
      'resourceOptimization': rawData.monitoring?.features?.resourceOptimization || 'Resource Optimization',
      'costAnalysis': rawData.monitoring?.features?.costAnalysis || 'Cost Analysis',
      'apiManagement': rawData.monitoring?.features?.apiManagement || 'API Management',
      'logAnalysis': rawData.monitoring?.features?.logAnalysis || 'Log Analysis'
    }
    return titles[key] || key
  }

  // Helper function to get field labels
  const getFieldLabel = (field: 'memory' | 'storage'): string => {
    const labels = {
      memory: rawData.techSpecs?.memory?.includes('Memory') ? 'Memory' :
              rawData.techSpecs?.memory?.includes('内存') ? '内存' : 'Memory',
      storage: rawData.techSpecs?.storage?.includes('Storage') ? 'Storage' :
               rawData.techSpecs?.storage?.includes('存储') ? '存储' : 'Storage'
    }
    return labels[field]
  }

  // Type guard for checking if object has required properties
  const isValidSpec = (spec: any): spec is { name: string; cpu: string; memory: string; storage: string; useCase: string } => {
    return spec && typeof spec === 'object' &&
           typeof spec.name === 'string' &&
           typeof spec.cpu === 'string' &&
           typeof spec.memory === 'string' &&
           typeof spec.storage === 'string' &&
           typeof spec.useCase === 'string'
  }

  const isValidUseCase = (useCase: any): useCase is { name: string; description: string; features?: string[] } => {
    return useCase && typeof useCase === 'object' &&
           typeof useCase.name === 'string' &&
           typeof useCase.description === 'string'
  }

  const isValidPlan = (plan: any): plan is { name: string; price: string; description: string } => {
    return plan && typeof plan === 'object' &&
           typeof plan.name === 'string' &&
           typeof plan.price === 'string' &&
           typeof plan.description === 'string'
  }

  const isValidAdvantage = (advantage: any): advantage is { title: string; description: string } => {
    return advantage && typeof advantage === 'object' &&
           typeof advantage.title === 'string' &&
           typeof advantage.description === 'string'
  }

  return (
    <div className="relative isolate min-h-screen">
      {/* Background Effects */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-primary/3 to-transparent dark:from-primary/10 dark:via-primary/5" />
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-primary/10 dark:bg-primary/20 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-primary/10 dark:bg-primary/20 rounded-full blur-3xl animate-pulse delay-1000" />
      </div>

      {/* Hero Section */}
      <section className="relative py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            className="mx-auto max-w-4xl text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="mb-6"
            >
              <Badge variant="outline" className="mb-4 px-4 py-2 text-sm font-medium border-primary/20 bg-primary/5 dark:border-primary/30 dark:bg-primary/10">
                <Cpu className="w-4 h-4 mr-2" />
                {t('badge')}
              </Badge>
            </motion.div>

            <motion.h1
              className="text-4xl font-bold tracking-tight sm:text-6xl lg:text-7xl mb-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <span className="bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent dark:from-primary dark:to-primary/90">
                {product.name}
              </span>
            </motion.h1>

            <motion.p
              className="text-xl leading-8 text-muted-foreground mb-8 max-w-3xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              {product.description}
            </motion.p>

            <motion.div
              className="flex flex-col sm:flex-row gap-4 justify-center mb-12"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
            >
              <Button asChild size="lg" className="bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary/80">
                <Link href="/contact-us">
                  {tCommon('contactUs')}
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              
              <Button variant="outline" size="lg">
                <Calendar className="mr-2 h-5 w-5" />
                {tCommon('learnMore')}
              </Button>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-muted/30">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold mb-4">{t('coreFeatures')}</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              {t('coreDescription')}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
            {product.features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -5 }}
                className="group"
              >
                <Card className="relative overflow-hidden border-0 bg-background/60 dark:bg-background/40 backdrop-blur-xl shadow-lg hover:shadow-xl transition-all duration-500 h-full">
                  <CardContent className="p-6">
                    <div className="flex items-center gap-4 mb-4">
                      <div className="p-3 rounded-xl bg-gradient-to-br from-primary to-primary/90 shadow-lg group-hover:scale-110 transition-transform duration-300">
                        <CheckCircle className="h-6 w-6 text-white" />
                      </div>
                      <h3 className="text-lg font-semibold group-hover:text-primary transition-colors">
                        {feature}
                      </h3>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Advantages Section */}
      <section className="py-16">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold mb-4">{rawData.keyAdvantages}</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              {rawData.advantages?.scalableComputing?.description?.includes('业务需求') ?
                '我们的专业优势让您的计算任务更加高效' :
                'Our professional advantages make your computing tasks more efficient'}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
            {Object.entries(rawData.advantages || {}).map(([key, advantage], index) => {
              if (!isValidAdvantage(advantage)) return null

              return (
                <motion.div
                  key={key}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  whileHover={{ y: -5 }}
                  className="group"
                >
                  <Card className="relative overflow-hidden border-0 bg-background/60 dark:bg-background/40 backdrop-blur-xl shadow-lg hover:shadow-xl transition-all duration-500 h-full">
                    <CardContent className="p-6">
                      <div className="flex items-start gap-4">
                        <div className="p-3 rounded-xl bg-gradient-to-br from-primary to-primary/80 shadow-lg group-hover:scale-110 transition-transform duration-300 flex-shrink-0">
                          {key === 'scalableComputing' && <Zap className="h-6 w-6 text-white" />}
                          {key === 'costOptimization' && <DollarSign className="h-6 w-6 text-white" />}
                          {key === 'highPerformance' && <Cpu className="h-6 w-6 text-white" />}
                          {key === 'quickDeployment' && <Clock className="h-6 w-6 text-white" />}
                          {key === 'reliableService' && <Shield className="h-6 w-6 text-white" />}
                          {key === 'globalCoverage' && <Globe className="h-6 w-6 text-white" />}
                        </div>
                        <div className="flex-1">
                          <h3 className="text-lg font-semibold mb-2 group-hover:text-primary transition-colors">
                            {advantage.title}
                          </h3>
                          <p className="text-sm text-muted-foreground leading-relaxed">
                            {advantage.description}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              )
            })}
          </div>
        </div>
      </section>

      {/* Performance Stats */}
      <section className="py-16">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold mb-4">{t('performanceMetrics')}</h2>
          </motion.div>

          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              viewport={{ once: true }}
            >
              <Card className="border-0 bg-background/60 dark:bg-background/40 backdrop-blur-xl shadow-lg hover:shadow-xl transition-all duration-300">
                <CardContent className="p-6 text-center">
                  <div className="p-3 rounded-xl bg-gradient-to-br from-primary to-primary/90 shadow-lg mb-4 inline-block">
                    <Server className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-primary mb-2">99.9%</h3>
                  <p className="text-sm text-muted-foreground">{rawData.techSpecs?.availability}</p>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="border-0 bg-background/60 dark:bg-background/40 backdrop-blur-xl shadow-lg hover:shadow-xl transition-all duration-300">
                <CardContent className="p-6 text-center">
                  <div className="p-3 rounded-xl bg-gradient-to-br from-primary to-primary/90 shadow-lg mb-4 inline-block">
                    <Zap className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-primary mb-2">5{rawData.minutes}</h3>
                  <p className="text-sm text-muted-foreground">{rawData.advantages?.quickDeployment?.title}</p>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <Card className="border-0 bg-background/60 dark:bg-background/40 backdrop-blur-xl shadow-lg hover:shadow-xl transition-all duration-300">
                <CardContent className="p-6 text-center">
                  <div className="p-3 rounded-xl bg-gradient-to-br from-primary to-primary/90 shadow-lg mb-4 inline-block">
                    <Activity className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-primary mb-2">60%</h3>
                  <p className="text-sm text-muted-foreground">{rawData.advantages?.costOptimization?.title}</p>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              viewport={{ once: true }}
            >
              <Card className="border-0 bg-background/60 dark:bg-background/40 backdrop-blur-xl shadow-lg hover:shadow-xl transition-all duration-300">
                <CardContent className="p-6 text-center">
                  <div className="p-3 rounded-xl bg-gradient-to-br from-primary to-primary/90 shadow-lg mb-4 inline-block">
                    <Globe className="h-6 w-6 text-white" />
                  </div>
                  <h3 className="text-2xl font-bold text-primary mb-2">{rawData.global}</h3>
                  <p className="text-sm text-muted-foreground">{rawData.techSpecs?.deployment}</p>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Computing Specs Section */}
      <section className="py-16 bg-muted/30">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold mb-4">{rawData.computingSpecs?.title}</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              {rawData.computingSpecs?.subtitle}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
            {Object.entries(rawData.computingSpecs || {})
              .filter(([key]) => key !== 'title' && key !== 'subtitle')
              .map(([key, spec], index) => {
                if (!isValidSpec(spec)) return null

                return (
                  <motion.div
                    key={key}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    whileHover={{ y: -5 }}
                    className="group"
                  >
                    <Card className={`border-0 backdrop-blur-xl shadow-lg hover:shadow-xl transition-all duration-500 h-full ${
                      key === 'standard'
                        ? 'bg-gradient-to-br from-primary/10 to-primary/5 ring-2 ring-primary/20'
                        : 'bg-background/60 dark:bg-background/40'
                    }`}>
                      <CardContent className="p-6">
                        <div className="text-center">
                          <div className="p-3 rounded-xl bg-gradient-to-br from-primary to-primary/80 shadow-lg mb-4 inline-block group-hover:scale-110 transition-transform duration-300">
                            {key === 'basic' && <Server className="h-6 w-6 text-white" />}
                            {key === 'standard' && <Cpu className="h-6 w-6 text-white" />}
                            {key === 'performance' && <Activity className="h-6 w-6 text-white" />}
                            {key === 'enterprise' && <Database className="h-6 w-6 text-white" />}
                          </div>
                          <h3 className="text-xl font-bold mb-4 group-hover:text-primary transition-colors">
                            {spec.name}
                          </h3>
                          <div className="space-y-3 text-sm">
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">CPU:</span>
                              <span className="font-medium">{spec.cpu}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">
                                {getFieldLabel('memory')}:
                              </span>
                              <span className="font-medium">{spec.memory}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-muted-foreground">
                                {getFieldLabel('storage')}:
                              </span>
                              <span className="font-medium">{spec.storage}</span>
                            </div>
                            <div className="pt-3 border-t border-muted">
                              <p className="text-xs text-muted-foreground">{spec.useCase}</p>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                )
              })}
          </div>
        </div>
      </section>

      {/* Use Cases Section */}
      <section className="py-16">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold mb-4">{rawData.useCases?.title}</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              {rawData.useCases?.subtitle}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
            {Object.entries(rawData.useCases || {})
              .filter(([key]) => key !== 'title' && key !== 'subtitle')
              .map(([key, useCase], index) => {
                if (!isValidUseCase(useCase)) return null

                return (
                  <motion.div
                    key={key}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    whileHover={{ y: -5 }}
                    className="group"
                  >
                    <Card className="border-0 bg-background/60 dark:bg-background/40 backdrop-blur-xl shadow-lg hover:shadow-xl transition-all duration-500 h-full">
                      <CardContent className="p-6">
                        <div className="flex items-start gap-4 mb-4">
                          <div className="p-3 rounded-xl bg-gradient-to-br from-primary to-primary/80 shadow-lg group-hover:scale-110 transition-transform duration-300 flex-shrink-0">
                            {key === 'scientificComputing' && <BarChart3 className="h-6 w-6 text-white" />}
                            {key === 'aiTraining' && <Brain className="h-6 w-6 text-white" />}
                            {key === 'bigDataProcessing' && <Database className="h-6 w-6 text-white" />}
                            {key === 'webServices' && <Globe className="h-6 w-6 text-white" />}
                            {key === 'rendering' && <Palette className="h-6 w-6 text-white" />}
                            {key === 'blockchain' && <LinkIcon className="h-6 w-6 text-white" />}
                          </div>
                          <div className="flex-1">
                            <h3 className="text-lg font-semibold mb-2 group-hover:text-primary transition-colors">
                              {useCase.name}
                            </h3>
                            <p className="text-sm text-muted-foreground mb-4 leading-relaxed">
                              {useCase.description}
                            </p>
                            <div className="space-y-2">
                              {useCase.features?.map((feature: string, featureIndex: number) => (
                                <div key={featureIndex} className="flex items-center gap-2">
                                  <CheckCircle className="h-4 w-4 text-primary flex-shrink-0" />
                                  <span className="text-xs text-muted-foreground">{feature}</span>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                )
              })}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-16 bg-muted/30">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold mb-4">{rawData.pricing?.title}</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              {rawData.pricing?.subtitle}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 gap-8 md:grid-cols-3">
            {Object.entries(rawData.pricing || {})
              .filter(([key]) => key !== 'title' && key !== 'subtitle')
              .map(([key, plan], index) => {
                if (!isValidPlan(plan)) return null

                return (
                  <motion.div
                    key={key}
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    whileHover={{ y: -5 }}
                    className="group"
                  >
                    <Card className={`border-0 backdrop-blur-xl shadow-lg hover:shadow-xl transition-all duration-500 h-full ${
                      key === 'reserved'
                        ? 'bg-gradient-to-br from-primary/10 to-primary/5 ring-2 ring-primary/20'
                        : 'bg-background/60 dark:bg-background/40'
                    }`}>
                      <CardContent className="p-8 text-center">
                        <div className="p-3 rounded-xl bg-gradient-to-br from-primary to-primary/80 shadow-lg mb-6 inline-block group-hover:scale-110 transition-transform duration-300">
                          {key === 'payAsYouGo' && <Clock className="h-6 w-6 text-white" />}
                          {key === 'reserved' && <TrendingUp className="h-6 w-6 text-white" />}
                          {key === 'dedicated' && <Server className="h-6 w-6 text-white" />}
                        </div>
                        <h3 className="text-xl font-bold mb-2">{plan.name}</h3>
                        <div className="text-3xl font-bold text-primary mb-2">{plan.price}</div>
                        <p className="text-sm text-muted-foreground mb-6">{plan.description}</p>
                        <Button
                          className={`w-full ${
                            key === 'reserved'
                              ? 'bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70'
                              : ''
                          }`}
                          variant={key === 'reserved' ? 'default' : 'outline'}
                        >
                          {key === 'dedicated' ? tCommon('contactUs') : rawData.startFreeTrial}
                        </Button>
                      </CardContent>
                    </Card>
                  </motion.div>
                )
              })}
          </div>
        </div>
      </section>

      {/* Monitoring Section */}
      <section className="py-16">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <motion.div
            className="text-center mb-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold mb-4">{rawData.monitoring?.title}</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              {rawData.monitoring?.subtitle}
            </p>
          </motion.div>

          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
            {Object.entries(rawData.monitoring?.features || {}).map(([key, feature], index) => {
              const featureText = typeof feature === 'string' ? feature : ''

              return (
                <motion.div
                  key={key}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  whileHover={{ y: -5 }}
                  className="group"
                >
                  <Card className="border-0 bg-background/60 dark:bg-background/40 backdrop-blur-xl shadow-lg hover:shadow-xl transition-all duration-500 h-full">
                    <CardContent className="p-6">
                      <div className="flex items-start gap-4">
                        <div className="p-3 rounded-xl bg-gradient-to-br from-primary to-primary/80 shadow-lg group-hover:scale-110 transition-transform duration-300 flex-shrink-0">
                          {key === 'performanceMonitoring' && <Monitor className="h-6 w-6 text-white" />}
                          {key === 'alertSystem' && <AlertTriangle className="h-6 w-6 text-white" />}
                          {key === 'resourceOptimization' && <TrendingUp className="h-6 w-6 text-white" />}
                          {key === 'costAnalysis' && <BarChart3 className="h-6 w-6 text-white" />}
                          {key === 'apiManagement' && <Settings className="h-6 w-6 text-white" />}
                          {key === 'logAnalysis' && <FileText className="h-6 w-6 text-white" />}
                        </div>
                        <div className="flex-1">
                          <h3 className="text-lg font-semibold mb-2 group-hover:text-primary transition-colors">
                            {getMonitoringTitle(key)}
                          </h3>
                          <p className="text-sm text-muted-foreground leading-relaxed">
                            {featureText}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              )
            })}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-br from-primary/5 to-primary/10 dark:from-primary/10 dark:to-primary/20">
        <div className="mx-auto max-w-4xl px-6 lg:px-8 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl font-bold mb-4">{t('readyToStart')}</h2>
            <p className="text-muted-foreground mb-8 max-w-2xl mx-auto">
              {t('ctaDescription')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary/80">
                <MessageCircle className="w-5 h-5 mr-2" />
                {tCommon('contactUs')}
              </Button>
              <Button variant="outline" size="lg">
                <Download className="w-5 h-5 mr-2" />
                {rawData.learnMore}
              </Button>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  )
}
