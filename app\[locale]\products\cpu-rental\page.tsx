import { Metadata } from 'next'
import { getTranslations } from 'next-intl/server'
import { getProductDetails } from '@/lib/products-data'
import { ProductDetailClient } from './components/ProductDetailClient'
import { notFound } from 'next/navigation'

const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://zerodots.tech'

interface Props {
  params: { locale: string }
}

export async function generateMetadata({ params: { locale } }: Props): Promise<Metadata> {
  const t = await getTranslations({ locale })
  const productDetails = getProductDetails(t)
  const product = productDetails['cpu-rental']

  if (!product) {
    return {
      title: 'Product Not Found',
      description: 'The requested product could not be found.'
    }
  }

  const isEnglish = locale === 'en'
  const title = isEnglish 
    ? `${product.name} - zerodots` 
    : `${product.name} - 零点科技`
  
  const description = product.description

  return {
    title,
    description,
    keywords: isEnglish 
      ? ['CPU rental', 'cloud computing', 'high performance computing', 'elastic scaling', 'scientific computing']
      : ['CPU算力租用', '云计算', '高性能计算', '弹性扩容', '科学计算'],
    openGraph: {
      title,
      description,
      url: `${baseUrl}/${locale}/products/cpu-rental`,
      siteName: isEnglish ? 'zerodots' : '零点科技',
      images: [
        {
          url: `${baseUrl}/og-cpu-rental.jpg`,
          width: 1200,
          height: 630,
          alt: product.name,
        },
      ],
      locale: isEnglish ? 'en_US' : 'zh_CN',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: [`${baseUrl}/og-cpu-rental.jpg`],
    },
    alternates: {
      canonical: `${baseUrl}/${locale}/products/cpu-rental`,
      languages: {
        'zh-CN': `${baseUrl}/zh/products/cpu-rental`,
        'en-US': `${baseUrl}/en/products/cpu-rental`,
      },
    },
  }
}

export default async function CPURentalPage({ params: { locale } }: Props) {
  const t = await getTranslations({ locale })
  const productDetails = getProductDetails(t)
  const product = productDetails['cpu-rental']

  if (!product) {
    notFound()
  }

  return <ProductDetailClient product={product} productSlug="cpu-rental" />
}
