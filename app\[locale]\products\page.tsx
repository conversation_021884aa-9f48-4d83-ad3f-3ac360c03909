
import { Metadata } from 'next'
import { ProductsClient } from "./components/ProductsClient"
import { getProducts } from "@/lib/products-data"
import { getTranslations } from 'next-intl/server'

const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://zerodots.tech';

interface Props {
  params: { locale: string }
}

export async function generateMetadata({ params: { locale } }: Props): Promise<Metadata> {
  const t = await getTranslations({ locale })
  const isEnglish = locale === 'en'

  const title = isEnglish
    ? 'Products & Services - zerodots'
    : '产品服务 - 零点科技'

  const description = isEnglish
    ? 'zerodots provides professional AI annotation, CPU rental, education management, and custom software development services. Our products cover AI services, cloud computing, EdTech, and custom development.'
    : '零点科技提供AI智能标注、CPU算力租用、教育培训管理和定制软件开发等专业服务。我们的产品覆盖AI服务、云计算、教育科技和定制开发四大领域，为企业提供全方位的技术解决方案。'

  const keywords = isEnglish
    ? ['zerodots products', 'AI annotation services', 'CPU rental', 'education management system', 'custom software development', 'enterprise solutions', 'cloud computing services', 'artificial intelligence products', 'EdTech platform', 'software customization']
    : ['零点科技产品', 'AI智能标注服务', 'CPU算力租用', '教育培训管理系统', '定制软件开发', '企业级解决方案', '云计算服务', '人工智能产品', '教育科技平台', '软件定制开发']

  return {
    title,
    description,
    keywords,
    openGraph: {
      title,
      description,
      url: `${baseUrl}/${locale}/products`,
      siteName: isEnglish ? 'zerodots' : '零点科技',
      images: [
        {
          url: `${baseUrl}/og-products.jpg`,
          width: 1200,
          height: 630,
          alt: isEnglish ? 'zerodots Products & Services' : '零点科技产品服务',
        },
      ],
      locale: isEnglish ? 'en_US' : 'zh_CN',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: [`${baseUrl}/og-products.jpg`],
    },
    alternates: {
      canonical: `${baseUrl}/${locale}/products`,
      languages: {
        'zh-CN': `${baseUrl}/zh/products`,
        'en-US': `${baseUrl}/en/products`,
      },
    },
  }
}

export default async function Products({ params: { locale } }: Props) {
  const t = await getTranslations({ locale })
  const products = getProducts(t)
 
  return (
    <>
      <ProductsClient products={products} />
    </>
  )
}