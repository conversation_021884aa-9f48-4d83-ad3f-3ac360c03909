import { MetadataRoute } from 'next'
import { getProducts } from '@/lib/products-data'

// Create a simple translation function for sitemap generation
const createSitemapTranslation = (key: string) => {
  // For sitemap, we'll use the Chinese keys as fallback since it's the original language
  const translations: Record<string, string> = {
    'products.aiAnnotation.name': 'AI智能标注平台',
    'products.cpuRental.name': 'CPU算力租用',
    'products.educationManagement.name': '教育培训管理系统',
    'products.dataAnnotationPro.name': '专业数据标注',
    'products.customWebDevelopment.name': '定制Web开发',
    'products.mobileAppDevelopment.name': '移动应用开发',
    'products.enterpriseSoftware.name': '企业软件定制'
  }
  return translations[key] || key
}

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://zerodots.tech'
  
  // 静态页面
  const staticPages = [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 1,
    },
    {
      url: `${baseUrl}/about`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/products`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.9,
    },
    {
      url: `${baseUrl}/cases`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/news`,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 0.7,
    },
    {
      url: `${baseUrl}/contact-us`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.6,
    },
    {
      url: `${baseUrl}/privacy`,
      lastModified: new Date(),
      changeFrequency: 'yearly' as const,
      priority: 0.3,
    },
    {
      url: `${baseUrl}/cookies`,
      lastModified: new Date(),
      changeFrequency: 'yearly' as const,
      priority: 0.3,
    },
  ]

  // 产品页面
  const products = getProducts(createSitemapTranslation)
  const productPages = products.map((product) => ({
    url: `${baseUrl}/products/${product.slug}`,
    lastModified: new Date(),
    changeFrequency: 'monthly' as const,
    priority: 0.8,
  }))

  // 新闻页面（示例，实际应该从数据库或API获取）
  const newsPages = [
    // {
    //   url: `${baseUrl}/news/1`,
    //   lastModified: new Date('2024-03-15'),
    //   changeFrequency: 'never' as const,
    //   priority: 0.6,
    // },
    // {
    //   url: `${baseUrl}/news/2`,
    //   lastModified: new Date('2024-03-20'),
    //   changeFrequency: 'never' as const,
    //   priority: 0.6,
    // },
  ]

  return [...staticPages, ...productPages]
}
