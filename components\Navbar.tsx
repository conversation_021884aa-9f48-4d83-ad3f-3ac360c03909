"use client"

import { useState, useEffect, useCallback, useMemo } from 'react'
import Link from 'next/link'
import { usePathname, useRouter } from 'next/navigation'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Menu, X, CircleDot } from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { useTranslations, useLocale } from '@/hooks/useTranslations'
import { LanguageSwitcher, MobileLanguageSwitcher } from '@/components/LanguageSwitcher'
import { ThemeToggle } from '@/components/ThemeToggle'

export default function Navbar() {
  const router = useRouter()
  const pathname = usePathname()
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const [scrolled, setScrolled] = useState(false)
  const [isMobile, setIsMobile] = useState(false)
  const t = useTranslations('navigation')
  const tCommon = useTranslations('common')
  const locale = useLocale()

  // 导航配置，使用国际化
  const navigation = useMemo(() => [
    { name: t('home'), href: `/${locale}` },
    { name: t('products'), href: `/${locale}/products` },
    { name: t('about'), href: `/${locale}/about` },
    // { name: t('careers'), href: `/${locale}/careers` },
  ], [t, locale])

  // 优化滚动处理函数
  const handleScroll = useCallback(() => {
    const shouldBeScrolled = window.scrollY > 0;
    if (scrolled !== shouldBeScrolled) {
      setScrolled(shouldBeScrolled);
    }
  }, [scrolled]);

  useEffect(() => {
    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [handleScroll]);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 1024);
    };

    handleResize();
    window.addEventListener('resize', handleResize);

    return () => window.removeEventListener('resize', handleResize);
  }, []);

  useEffect(() => {
    if (!isMobile) {
      setMobileMenuOpen(false);
    }
  }, [isMobile]);

  // 缓存导航项的激活状态判断
  const isActiveLink = useCallback((href: string) => pathname === href, [pathname]);

  // 缓存公共样式
  const headerClasses = useMemo(() => cn(
    "fixed inset-x-0 top-0 z-50 transition-all duration-500",
    scrolled 
      ? "backdrop-blur-xl bg-background/70 border-b border-primary/10 shadow-sm" 
      : "bg-transparent"
  ), [scrolled]);

  // 缓存按钮点击处理函数
  const handleContactClick = useCallback(() => {
    router.push(`/${locale}/contact-us`);
    if (isMobile) {
      setMobileMenuOpen(false);
    }
  }, [router, isMobile, locale]);

  const handleMobileMenuClose = useCallback(() => {
    setMobileMenuOpen(false);
  }, []);

  // 缓存动画配置
  const animations = useMemo(() => ({
    logo: {
      initial: { opacity: 0, x: -20 },
      animate: { opacity: 1, x: 0 },
      transition: { duration: 0.5 }
    },
    mobileMenu: {
      initial: { opacity: 0, x: "100%" },
      animate: { opacity: 1, x: 0 },
      exit: { opacity: 0, x: "100%" },
      transition: { type: "spring", bounce: 0, duration: 0.4 }
    }
  }), []);

  return (
    <header className={headerClasses}>
      <nav className="flex items-center justify-between p-6 lg:px-8 max-w-7xl mx-auto" aria-label="Global">
        <motion.div 
          className="flex lg:flex-1"
          {...animations.logo}
        >
          <Link href={`/${locale}`} className="-m-1.5 p-1.5 flex items-center gap-2 group">
            <div className="relative">
              <CircleDot className="h-8 w-8 text-primary transition-transform duration-300 group-hover:scale-110" />
              <div className="absolute inset-0 bg-primary/20 blur-xl rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            </div>
            <span className="font-bold text-xl bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
              ZeroDots
            </span>
          </Link>
        </motion.div>

        <div className="flex lg:hidden">
          <Button
            variant="ghost"
            className="-m-2.5 inline-flex items-center justify-center rounded-md p-2.5 hover:bg-primary/10"
            onClick={() => setMobileMenuOpen(true)}
          >
            <span className="sr-only">Open main menu</span>
            <Menu className="h-6 w-6 text-primary" aria-hidden="true" />
          </Button>
        </div>

        <div className="hidden lg:flex lg:gap-x-12">
          {navigation.map((item, i) => (
            <motion.div
              key={item.name}
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: i * 0.1 }}
            >
              <Link
                href={item.href}
                className={cn(
                  'relative text-sm font-semibold leading-6 transition-all duration-300 hover:text-primary group',
                  isActiveLink(item.href) ? 'text-primary' : 'text-muted-foreground'
                )}
              >
                {item.name}
                <span className={cn(
                  "absolute -bottom-2 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full",
                  isActiveLink(item.href) && "w-full"
                )} />
              </Link>
            </motion.div>
          ))}
        </div>

        <motion.div
          className="hidden lg:flex lg:flex-1 lg:justify-end lg:items-center lg:gap-4"
          {...animations.logo}
        >
          <ThemeToggle />
          <LanguageSwitcher />
          <Button
            variant="default"
            className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 transition-all duration-300 hover:scale-105"
            onClick={handleContactClick}
          >
            {tCommon('contactUs')}
          </Button>
        </motion.div>
      </nav>

      <AnimatePresence>
        {mobileMenuOpen && (
          <motion.div
            {...animations.mobileMenu}
            className="fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-background/95 backdrop-blur-xl px-6 py-6 sm:max-w-sm border-l border-primary/10 top-0"
          >
            <div className="flex items-center justify-between">
              <Link href={`/${locale}`} className="-m-1.5 p-1.5 flex items-center gap-2 group">
                <CircleDot className="h-8 w-8 text-primary transition-transform duration-300 group-hover:scale-110" />
                <span className="font-bold text-xl bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                  0dot
                </span>
              </Link>
              <Button
                variant="ghost"
                className="-m-2.5 rounded-md p-2.5 hover:bg-primary/10"
                onClick={handleMobileMenuClose}
              >
                <span className="sr-only">Close menu</span>
                <X className="h-6 w-6 text-primary" aria-hidden="true" />
              </Button>
            </div>

            <div className="mt-6 flow-root">
              <div className="-my-6 divide-y divide-primary/10">
                <div className="space-y-2 py-6">
                  {navigation.map((item) => (
                    <Link
                      key={item.name}
                      href={item.href}
                      className={cn(
                        'group -mx-3 flex items-center gap-2 rounded-lg px-3 py-2 text-base font-semibold leading-7 transition-colors duration-300',
                        pathname === item.href 
                          ? 'text-primary bg-primary/10' 
                          : 'text-muted-foreground hover:text-primary hover:bg-primary/5'
                      )}
                      onClick={handleMobileMenuClose}
                    >
                      <span className="w-1.5 h-1.5 rounded-full bg-primary/50 group-hover:scale-150 transition-transform duration-300" />
                      {item.name}
                    </Link>
                  ))}
                </div>
                <div className="py-6 space-y-4">
                  <Button
                    className="w-full bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70"
                    variant="default"
                    onClick={handleContactClick}
                  >
                    {tCommon('contactUs')}
                  </Button>
                  <div className="flex items-center justify-between">
                    <MobileLanguageSwitcher />
                    <ThemeToggle />
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </header>
  )
}
