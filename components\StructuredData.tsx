'use client'

export function StructuredData() {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://zerodots.tech';
  
  const organizationData = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "零点科技",
    "alternateName": "ZeroDots",
    "url": baseUrl,
    "logo": `${baseUrl}/logo.png`,
    "description": "零点科技专注于AI智能标注、CPU算力租用、教育培训管理和定制软件开发，为企业提供专业的技术解决方案和服务。",
    "foundingDate": "2025",
    "address": {
      "@type": "PostalAddress",
      "streetAddress": "科技园区888号",
      "addressLocality": "朝阳区",
      "addressRegion": "北京市",
      "postalCode": "100000",
      "addressCountry": "CN"
    },
    "contactPoint": [
      {
        "@type": "ContactPoint",
        "telephone": "+86-************",
        "contactType": "customer service",
        "availableLanguage": ["Chinese", "English"]
      },
      {
        "@type": "ContactPoint",
        "email": "<EMAIL>",
        "contactType": "technical support",
        "availableLanguage": ["Chinese", "English"]
      },
      {
        "@type": "ContactPoint",
        "email": "<EMAIL>",
        "contactType": "sales",
        "availableLanguage": ["Chinese", "English"]
      }
    ],
    "sameAs": [
      "https://weibo.com/0dot",
      "https://www.linkedin.com/company/0dot",
      "https://github.com/0dot"
    ],
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "零点科技服务目录",
      "itemListElement": [
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "AI智能标注服务",
            "description": "专业的AI数据标注服务，支持图像、文本、语音等多模态数据标注"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "CPU算力租用服务",
            "description": "灵活的云计算资源租用服务，提供高性能CPU集群"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "教育培训管理系统",
            "description": "一站式教育培训管理平台，涵盖课程管理、学员管理等"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "定制软件开发",
            "description": "企业级定制软件开发服务，涵盖Web应用、移动应用等"
          }
        }
      ]
    }
  };

  const websiteData = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "零点科技",
    "url": baseUrl,
    "description": "零点科技官方网站 - 企业级AI与云计算解决方案提供商",
    "publisher": {
      "@type": "Organization",
      "name": "零点科技"
    },
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": `${baseUrl}/search?q={search_term_string}`
      },
      "query-input": "required name=search_term_string"
    }
  };

  const breadcrumbData = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "首页",
        "item": baseUrl
      }
    ]
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(organizationData),
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(websiteData),
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(breadcrumbData),
        }}
      />
    </>
  );
}
