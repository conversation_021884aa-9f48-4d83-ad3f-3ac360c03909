'use client'

import React from 'react'
import { motion } from "framer-motion"
import { Building2, Users, TrendingUp, Award, Star, ArrowRight } from 'lucide-react'
import { Button } from '../ui/button'
import Link from 'next/link'
import { useRawTranslations } from '@/hooks/useTranslations'

function ClientShowcase() {
  const rawData = useRawTranslations('home.clientShowcase')

  const iconMap = [Building2, Users, TrendingUp, Award]

  const clientCases = rawData.cases || [
    {
      id: 'ai-annotation-case',
      title: '大型电商平台AI标注项目',
      client: '某知名电商平台',
      industry: '电子商务',
      challenge: '需要对海量商品图片进行精准标注，提升搜索推荐算法效果',
      solution: '部署AI智能标注系统，实现自动化图片标注和质量控制',
      results: [
        { metric: '标注精度', value: '99.5%', improvement: '+15%' },
        { metric: '处理速度', value: '10万张/天', improvement: '+500%' },
        { metric: '成本节省', value: '60%', improvement: '-60%' }
      ],
      tags: ['AI标注', '图像识别', '自动化'],
      color: 'from-blue-500 to-blue-600'
    },
    {
      id: 'cpu-rental-case',
      title: '科研院所高性能计算服务',
      client: '某科研院所',
      industry: '科学研究',
      challenge: '大规模科学计算需求，本地算力不足，采购成本高',
      solution: '提供弹性CPU算力租用服务，按需扩容，灵活配置',
      results: [
        { metric: '计算性能', value: '100倍提升', improvement: '+10000%' },
        { metric: '成本节省', value: '70%', improvement: '-70%' },
        { metric: '部署时间', value: '1小时', improvement: '-95%' }
      ],
      tags: ['高性能计算', '弹性扩容', '科学计算'],
      color: 'from-green-500 to-green-600'
    },
    {
      id: 'education-case',
      title: '在线教育平台管理系统',
      client: '某教育集团',
      industry: '在线教育',
      challenge: '多校区学员管理复杂，考试系统不统一，证书管理困难',
      solution: '构建一站式教育培训管理平台，统一管理流程',
      results: [
        { metric: '管理效率', value: '80%提升', improvement: '+80%' },
        { metric: '学员满意度', value: '98%', improvement: '+25%' },
        { metric: '运营成本', value: '50%节省', improvement: '-50%' }
      ],
      tags: ['教育管理', '在线考试', '证书系统'],
      color: 'from-purple-500 to-purple-600'
    }
  ]

  const clientStats = rawData.stats?.map((stat: any, index: number) => ({
    ...stat,
    icon: iconMap[index]
  })) || [
    { icon: Building2, label: '服务企业', value: '500+', description: '覆盖多个行业' },
    { icon: Users, label: '项目交付', value: '1000+', description: '成功案例' },
    { icon: TrendingUp, label: '客户满意度', value: '98%', description: '持续优化' },
    { icon: Award, label: '行业认证', value: '20+', description: '权威认可' }
  ]

  return (
    <section className="py-24 sm:py-32 relative overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0 bg-gradient-to-b from-slate-50 via-blue-50/30 to-white" />
      <div className="absolute top-0 left-0 w-full h-full opacity-30">
        <div className="absolute top-20 right-20 w-96 h-96 rounded-full blur-3xl animate-float" style={{ background: 'linear-gradient(135deg, rgb(59 130 246), rgb(37 99 235))' }} />
        <div className="absolute bottom-20 left-20 w-72 h-72 rounded-full blur-3xl animate-float" style={{ background: 'linear-gradient(135deg, rgb(99 102 241), rgb(168 85 247))', animationDelay: '3s' }} />
      </div>

      <div className="relative mx-auto max-w-7xl px-6 lg:px-8">
        {/* 标题区域 */}
        <motion.div
          className="mx-auto max-w-3xl text-center mb-20"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-lg font-semibold leading-7 mb-4 text-blue-600">{rawData.sectionTitle || '客户案例'}</h2>
          <p className="text-4xl font-bold tracking-tight sm:text-5xl lg:text-6xl text-gradient-modern mb-8">
            {rawData.title || '成功案例展示'}
          </p>
          <p className="text-xl leading-relaxed text-slate-600">
            {rawData.subtitle || '与众多知名企业合作，为客户创造实际价值，推动业务增长'}
          </p>
        </motion.div>

        {/* 客户统计 */}
        <motion.div
          className="grid grid-cols-2 lg:grid-cols-4 gap-8 mb-20"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
        >
          {clientStats.map((stat: any, index:number) => (
            <div key={stat.label} className="text-center group">
              <div className="flex justify-center mb-4">
                <div className="p-4 rounded-2xl bg-white shadow-lg group-hover:shadow-xl transition-all duration-300 hover-lift">
                  <stat.icon className="h-8 w-8 text-blue-600" />
                </div>
              </div>
              <div className="text-2xl font-bold text-slate-800 mb-1">{stat.value}</div>
              <div className="text-sm font-medium text-slate-600 mb-1">{stat.label}</div>
              <div className="text-xs text-slate-500">{stat.description}</div>
            </div>
          ))}
        </motion.div>

        {/* 案例展示 */}
        <div className="space-y-12">
          {clientCases.map((clientCase:any, index: number) => (
            <motion.div
              key={clientCase.id}
              initial={{ opacity: 0, y: 40 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.2 }}
              viewport={{ once: true }}
            >
              <CaseCard clientCase={clientCase} index={index} labels={rawData.labels} />
            </motion.div>
          ))}
        </div>

        {/* 底部CTA */}
        <motion.div
          className="text-center mt-20"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
        >
          <Button asChild className="btn-modern shadow-button-modern group px-8 py-4 text-lg">
            <Link href="/cases">
              {rawData.labels?.viewMoreCases || '查看更多案例'}
              <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
            </Link>
          </Button>
        </motion.div>
      </div>
    </section>
  )
}

// 案例卡片组件
function CaseCard({ clientCase, index, labels }: { clientCase: any, index: number, labels?: any }) {
  const isEven = index % 2 === 0

  return (
    <div className={`grid grid-cols-1 lg:grid-cols-2 gap-12 items-center ${!isEven ? 'lg:grid-flow-col-dense' : ''}`}>
      {/* 案例信息 */}
      <div className={`${!isEven ? 'lg:col-start-2' : ''}`}>
        <div className="card-modern p-8 hover-glow">
          {/* 标签 */}
          <div className="flex flex-wrap gap-2 mb-6">
            {clientCase.tags.map((tag: string) => (
              <span
                key={tag}
                className="inline-flex items-center rounded-full px-3 py-1.5 text-sm font-medium"
                style={{
                  backgroundColor: 'rgba(59, 130, 246, 0.08)',
                  color: 'rgb(59 130 246)'
                }}
              >
                {tag}
              </span>
            ))}
          </div>

          {/* 标题和客户 */}
          <h3 className="text-2xl font-bold text-slate-800 mb-4 group-hover:text-gradient-modern transition-all duration-300">
            {clientCase.title}
          </h3>
          <div className="flex items-center gap-4 mb-6">
            <span className="text-blue-600 font-medium">{clientCase.client}</span>
            <span className="text-slate-400">•</span>
            <span className="text-slate-600">{clientCase.industry}</span>
          </div>

          {/* 挑战和解决方案 */}
          <div className="space-y-4 mb-8">
            <div>
              <h4 className="font-semibold text-slate-800 mb-2">{labels?.challenge || '挑战'}</h4>
              <p className="text-slate-600 text-sm leading-relaxed">{clientCase.challenge}</p>
            </div>
            <div>
              <h4 className="font-semibold text-slate-800 mb-2">{labels?.solution || '解决方案'}</h4>
              <p className="text-slate-600 text-sm leading-relaxed">{clientCase.solution}</p>
            </div>
          </div>

          {/* 成果展示 */}
          <div>
            <h4 className="font-semibold text-slate-800 mb-4">{labels?.results || '项目成果'}</h4>
            <div className="grid grid-cols-3 gap-4">
              {clientCase.results.map((result: any, idx: number) => (
                <div key={result.metric} className="text-center">
                  <div className="text-lg font-bold text-blue-600 mb-1">{result.value}</div>
                  <div className="text-xs text-slate-600 mb-1">{result.metric}</div>
                  <div className="text-xs text-green-600 font-medium">{result.improvement}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* 视觉元素 */}
      <div className={`${!isEven ? 'lg:col-start-1' : ''}`}>
        <div className="relative group">
          <div className={`w-full h-80 rounded-3xl bg-gradient-to-br ${clientCase.color} p-8 flex items-center justify-center shadow-2xl group-hover:scale-105 transition-transform duration-300`}>
            <div className="text-center text-white">
              <Star className="h-16 w-16 mx-auto mb-4 opacity-80" />
              <h4 className="text-2xl font-bold mb-2">{labels?.successCase || '成功案例'}</h4>
              <p className="text-lg opacity-90">{clientCase.industry}</p>
            </div>
          </div>
          <div className="absolute inset-0 rounded-3xl blur-xl opacity-0 group-hover:opacity-30 transition-opacity duration-300" style={{ background: clientCase.color.replace('from-', '').replace(' to-', ', ').replace('500', '400').replace('600', '500') }} />
        </div>
      </div>
    </div>
  )
}

export default ClientShowcase
