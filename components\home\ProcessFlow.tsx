'use client'

import React from 'react'
import { motion } from "framer-motion"
import { MessageSquare, FileSearch, Cog, Rocket, CheckCircle, ArrowRight } from 'lucide-react'
import { useRawTranslations } from '@/hooks/useTranslations'

function ProcessFlow() {
  const rawData = useRawTranslations('home.processFlow')

  const iconMap = [MessageSquare, FileSearch, Cog, Rocket, CheckCircle]
  const colorMap = [
    'from-blue-500 to-blue-600',
    'from-indigo-500 to-indigo-600',
    'from-purple-500 to-purple-600',
    'from-green-500 to-green-600',
    'from-orange-500 to-orange-600'
  ]

  const processes = rawData.processes?.map((process: any, index: number) => ({
    ...process,
    icon: iconMap[index],
    color: colorMap[index]
  })) || []

  return (
    <section className="py-24 sm:py-32 relative overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0 bg-gradient-to-b from-blue-50/30 via-white to-slate-50" />
      <div className="absolute top-0 right-0 w-96 h-96 rounded-full blur-3xl opacity-20 animate-float" style={{ background: 'linear-gradient(135deg, rgb(59 130 246), rgb(99 102 241))' }} />
      <div className="absolute bottom-0 left-0 w-72 h-72 rounded-full blur-3xl opacity-15 animate-float" style={{ background: 'linear-gradient(135deg, rgb(168 85 247), rgb(59 130 246))', animationDelay: '2s' }} />

      <div className="relative mx-auto max-w-7xl px-6 lg:px-8">
        {/* 标题区域 */}
        <motion.div
          className="mx-auto max-w-3xl text-center mb-20"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-lg font-semibold leading-7 mb-4 text-blue-600">{rawData.title || '服务流程'}</h2>
          <p className="text-4xl font-bold tracking-tight sm:text-5xl lg:text-6xl text-gradient-modern mb-8">
            {rawData.subtitle || '专业的服务流程'}
          </p>
          <p className="text-xl leading-relaxed text-slate-600">
            {rawData.description || '从需求分析到运维支持，我们提供全流程的专业服务，确保项目成功交付'}
          </p>
        </motion.div>

        {/* 流程步骤 */}
        <div className="relative">
          {/* 连接线 */}
          <div className="hidden lg:block absolute top-1/2 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-200 via-indigo-200 to-purple-200 transform -translate-y-1/2" />
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8 lg:gap-4">
            {processes.map((process: any, index: number) => (
              <motion.div
                key={process.step}
                initial={{ opacity: 0, y: 40 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="relative"
              >
                <ProcessCard process={process} index={index} />
              </motion.div>
            ))}
          </div>
        </div>

        {/* 底部统计 */}
        <motion.div
          className="mt-20 grid grid-cols-1 md:grid-cols-3 gap-8"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.5 }}
          viewport={{ once: true }}
        >
          {rawData.stats?.map((stat: any, index: number) => (
            <div key={index} className="text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">{stat.value}</div>
              <div className="text-slate-600">{stat.label}</div>
            </div>
          ))}
        </motion.div>
      </div>
    </section>
  )
}

// 流程卡片组件
function ProcessCard({ process, index }: { process: any, index: number }) {
  return (
    <div className="relative group">
      {/* 步骤编号 */}
      <div className="flex justify-center mb-6">
        <div className="relative">
          <div className={`w-16 h-16 rounded-full bg-gradient-to-br ${process.color} flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300 relative z-10`}>
            <span className="text-white font-bold text-lg">{process.step}</span>
          </div>
          <div className="absolute inset-0 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" style={{ background: 'rgba(59, 130, 246, 0.3)' }} />
        </div>
      </div>

      {/* 卡片内容 */}
      <div className="card-modern p-6 text-center hover-glow group-hover:scale-105 transition-all duration-300">
        {/* 图标 */}
        <div className="flex justify-center mb-4">
          <div className="p-3 rounded-xl bg-slate-50 group-hover:bg-blue-50 transition-colors duration-300">
            <process.icon className="h-6 w-6 text-slate-600 group-hover:text-blue-600 transition-colors duration-300" />
          </div>
        </div>

        {/* 标题和描述 */}
        <h3 className="text-lg font-bold text-slate-800 mb-3 group-hover:text-gradient-modern transition-all duration-300">
          {process.title}
        </h3>
        <p className="text-sm text-slate-600 mb-6 leading-relaxed">
          {process.description}
        </p>

        {/* 详细功能 */}
        <div className="space-y-2">
          {process.details.map((detail: string, idx: number) => (
            <motion.div
              key={detail}
              initial={{ opacity: 0, x: -10 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 + idx * 0.05 }}
              viewport={{ once: true }}
              className="flex items-center justify-center gap-2 text-sm text-slate-500"
            >
              <div className="w-1.5 h-1.5 rounded-full bg-blue-400" />
              <span>{detail}</span>
            </motion.div>
          ))}
        </div>
      </div>

      {/* 箭头连接器 (仅在大屏幕显示) */}
      {index < 4 && (
        <div className="hidden lg:block absolute top-8 -right-4 z-20">
          <ArrowRight className="h-6 w-6 text-blue-300" />
        </div>
      )}
    </div>
  )
}

export default ProcessFlow
