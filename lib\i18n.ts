// 简化的多语言实现
export const locales = ['zh', 'en'] as const;
export type Locale = typeof locales[number];

export const defaultLocale: Locale = 'zh';

// 语言配置
export const languages = {
  zh: {
    name: '中文',
    flag: '🇨🇳',
    dir: 'ltr'
  },
  en: {
    name: 'English', 
    flag: '🇺🇸',
    dir: 'ltr'
  }
} as const;

// 获取语言数据
export async function getMessages(locale: Locale) {
  try {
    const messages = await import(`../messages/${locale}.json`);
    return messages.default;
  } catch (error) {
    console.error(`Failed to load messages for locale: ${locale}`, error);
    // 回退到默认语言
    if (locale !== defaultLocale) {
      const fallbackMessages = await import(`../messages/${defaultLocale}.json`);
      return fallbackMessages.default;
    }
    return {};
  }
}

// 检查是否为有效的语言代码
export function isValidLocale(locale: string): locale is Locale {
  return locales.includes(locale as Locale);
}

// 从路径中提取语言代码
export function getLocaleFromPathname(pathname: string): Locale {
  const segments = pathname.split('/');
  const potentialLocale = segments[1];
  
  if (potentialLocale && isValidLocale(potentialLocale)) {
    return potentialLocale;
  }
  
  return defaultLocale;
}

// 移除路径中的语言前缀
export function removeLocaleFromPathname(pathname: string, locale: Locale): string {
  if (locale === defaultLocale) return pathname;
  
  const localePrefix = `/${locale}`;
  if (pathname.startsWith(localePrefix)) {
    return pathname.slice(localePrefix.length) || '/';
  }
  
  return pathname;
}

// 添加语言前缀到路径
export function addLocaleToPathname(pathname: string, locale: Locale): string {
  if (locale === defaultLocale) return pathname;
  
  return `/${locale}${pathname}`;
}
