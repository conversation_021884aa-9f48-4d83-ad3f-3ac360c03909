type TranslationFunction = (key: string) => string;

// Function to get products with translations - Core products only
export const getProducts = (t: TranslationFunction) => [
  {
    slug: "ai-annotation",
    name: t("products.aiAnnotation.name"),
    description: t("products.aiAnnotation.description"),
    iconName: "Brain",
    features: [
      { text: t("products.aiAnnotation.features.imageAnnotation"), iconName: "Eye" },
      { text: t("products.aiAnnotation.features.textAnnotation"), iconName: "Database" },
      { text: t("products.aiAnnotation.features.audioAnnotation"), iconName: "Target" },
      { text: t("products.aiAnnotation.features.qualityControl"), iconName: "CheckCircle2" }
    ],
    highlight: t("products.aiAnnotation.highlight"),
    price: t("products.aiAnnotation.price"),
    category: t("products.aiAnnotation.category")
  },
  {
    slug: "cpu-rental",
    name: t("products.cpuRental.name"),
    description: t("products.cpuRental.description"),
    iconName: "Cpu",
    features: [
      { text: t("products.cpuRental.features.highPerformance"), iconName: "Zap" },
      { text: t("products.cpuRental.features.elasticScaling"), iconName: "Globe" },
      { text: t("products.cpuRental.features.payAsYouGo"), iconName: "BarChart" },
      { text: t("products.cpuRental.features.monitoring247"), iconName: "Shield" }
    ],
    highlight: t("products.cpuRental.highlight"),
    price: t("products.cpuRental.price"),
    category: t("products.cpuRental.category")
  },
  {
    slug: "education-management",
    name: t("products.educationManagement.name"),
    description: t("products.educationManagement.description"),
    iconName: "GraduationCap",
    features: [
      { text: t("products.educationManagement.features.courseManagement"), iconName: "BookOpen" },
      { text: t("products.educationManagement.features.onlineExam"), iconName: "Target" },
      { text: t("products.educationManagement.features.studentTracking"), iconName: "Users" },
      { text: t("products.educationManagement.features.certificateSystem"), iconName: "Award" }
    ],
    highlight: t("products.educationManagement.highlight"),
    price: t("products.educationManagement.price"),
    category: t("products.educationManagement.category")
  },
  {
    slug: "custom-development",
    name: t("products.customDevelopment.name"),
    description: t("products.customDevelopment.description"),
    iconName: "Code",
    features: [
      { text: t("products.customDevelopment.features.webDevelopment"), iconName: "Globe" },
      { text: t("products.customDevelopment.features.mobileDevelopment"), iconName: "Smartphone" },
      { text: t("products.customDevelopment.features.enterpriseSoftware"), iconName: "Building" },
      { text: t("products.customDevelopment.features.systemIntegration"), iconName: "Settings" }
    ],
    highlight: t("products.customDevelopment.highlight"),
    price: t("products.customDevelopment.price"),
    category: t("products.customDevelopment.category")
  }
];

// Function to get product details with translations
export const getProductDetails = (t: TranslationFunction) => ({
  "ai-annotation": {
    name: t("products.aiAnnotation.name"),
    description: t("products.aiAnnotation.description"),
    features: [
      t("products.aiAnnotation.features.imageAnnotation"),
      t("products.aiAnnotation.features.textAnnotation"),
      t("products.aiAnnotation.features.audioAnnotation"),
      t("products.aiAnnotation.features.qualityControl")
    ],
    techSpecs: {
      deployment: t("productDetails.aiAnnotation.techSpecs.deployment"),
      security: t("productDetails.aiAnnotation.techSpecs.security"),
      availability: t("productDetails.aiAnnotation.techSpecs.availability"),
      support: t("productDetails.aiAnnotation.techSpecs.support")
    }
  },
  "cpu-rental": {
    name: t("products.cpuRental.name"),
    description: t("products.cpuRental.description"),
    features: [
      t("products.cpuRental.features.highPerformance"),
      t("products.cpuRental.features.elasticScaling"),
      t("products.cpuRental.features.payAsYouGo"),
      t("products.cpuRental.features.monitoring247")
    ],
    techSpecs: {
      deployment: t("productDetails.cpuRental.techSpecs.deployment"),
      security: t("productDetails.cpuRental.techSpecs.security"),
      availability: t("productDetails.cpuRental.techSpecs.availability"),
      support: t("productDetails.cpuRental.techSpecs.support")
    }
  },
  "education-management": {
    name: t("products.educationManagement.name"),
    description: t("products.educationManagement.description"),
    features: [
      t("productDetails.educationManagement.features.courseManagement"),
      t("productDetails.educationManagement.features.studentManagement"),
      t("productDetails.educationManagement.features.onlineExam"),
      t("productDetails.educationManagement.features.certificateSystem"),
      t("productDetails.educationManagement.features.paymentSystem"),
      t("productDetails.educationManagement.features.dataAnalytics"),
      t("productDetails.educationManagement.features.mobileApp"),
      t("productDetails.educationManagement.features.liveStreaming"),
      t("productDetails.educationManagement.features.discussionForum")
    ],
    techSpecs: {
      deployment: t("productDetails.educationManagement.techSpecs.deployment"),
      security: t("productDetails.educationManagement.techSpecs.security"),
      availability: t("productDetails.educationManagement.techSpecs.availability"),
      support: t("productDetails.educationManagement.techSpecs.support")
    },
    featureList: [
      {
        title: t("educationManagement.coreFeaturesTitle"),
        description: t("educationManagement.platformDescription"),
        features: [
          {
            name: t("educationManagement.courseManagement"),
            description: t("educationManagement.courseManagementDesc"),
            icon: "BookOpen"
          },
          {
            name: t("educationManagement.studentManagement"),
            description: t("educationManagement.studentManagementDesc"),
            icon: "Users"
          },
          {
            name: t("educationManagement.onlineExam"),
            description: t("educationManagement.onlineExamDesc"),
            icon: "Target"
          }
        ]
      }
    ],
    benefits: [
      {
        title: t("educationManagement.improveEfficiency"),
        description: t("educationManagement.improveEfficiencyDesc")
      },
      {
        title: t("educationManagement.personalizedLearning"),
        description: t("educationManagement.personalizedLearningDesc")
      },
      {
        title: t("educationManagement.comprehensiveAnalytics"),
        description: t("educationManagement.comprehensiveAnalyticsDesc")
      }
    ]
  },
  "custom-development": {
    name: t("products.customDevelopment.name"),
    description: t("products.customDevelopment.description"),
    features: [
      t("products.customDevelopment.features.webDevelopment"),
      t("products.customDevelopment.features.mobileDevelopment"),
      t("products.customDevelopment.features.enterpriseSoftware"),
      t("products.customDevelopment.features.systemIntegration")
    ],
    techSpecs: {
      deployment: t("productDetails.customDevelopment.techSpecs.deployment"),
      security: t("productDetails.customDevelopment.techSpecs.security"),
      availability: t("productDetails.customDevelopment.techSpecs.availability"),
      support: t("productDetails.customDevelopment.techSpecs.support")
    }
  }
});

// Legacy exports for backward compatibility
export const products = [];
export const productDetails = {};
