{"title": "Join Us", "subtitle": "Create the Future with ZeroDots", "description": "We are looking for passionate talents to join our team and drive the development of AI and cloud computing technologies together", "hero": {"title": "Career Opportunities", "subtitle": "Start Your New Career Chapter at ZeroDots", "description": "Join us to work with top industry talents and achieve your career goals in the rapidly developing AI and cloud computing field"}, "whyJoinUs": {"title": "Why Choose ZeroDots", "subtitle": "We provide excellent work environment and development opportunities", "benefits": [{"title": "Cutting-edge Technology", "description": "Participate in the latest AI technology R&D and access industry-leading projects", "icon": "brain"}, {"title": "Team Collaboration", "description": "Collaborate with excellent technical teams to solve challenging problems together", "icon": "users"}, {"title": "Growth Space", "description": "Comprehensive training system and career development path", "icon": "trending-up"}, {"title": "Work Environment", "description": "Modern office environment with flexible working arrangements", "icon": "building"}, {"title": "Compensation & Benefits", "description": "Competitive salary and comprehensive benefits package", "icon": "gift"}, {"title": "Innovation Culture", "description": "Encourage innovative thinking and support the realization of new ideas", "icon": "lightbulb"}]}, "openPositions": {"title": "Open Positions", "subtitle": "Discover career opportunities that suit you", "noPositions": "No open positions at the moment", "viewDetails": "View Details", "applyNow": "Apply Now"}, "positions": [{"id": "senior-ai-engineer", "title": "Senior AI Engineer", "department": "R&D Department", "location": "Beijing", "type": "Full-time", "experience": "3-5 years", "salary": "25K-40K", "description": "Responsible for AI algorithm R&D and optimization, participating in core technology development of intelligent annotation platform", "requirements": ["Bachelor's degree or above in Computer Science or related field", "3+ years of machine learning/deep learning project experience", "Proficient in Python, TensorFlow/PyTorch and other tech stacks", "Strong algorithm design and optimization capabilities", "Experience in computer vision or natural language processing preferred"], "responsibilities": ["Design and implement AI algorithm models", "Optimize model performance and accuracy", "Participate in product technical architecture design", "Guide junior engineers' technical growth", "Track cutting-edge technology development trends"], "tags": ["AI", "Machine Learning", "Python", "Deep Learning"]}, {"id": "cloud-architect", "title": "Cloud Architect", "department": "Infrastructure Department", "location": "Shanghai", "type": "Full-time", "experience": "5-8 years", "salary": "30K-50K", "description": "Responsible for cloud computing infrastructure design and optimization, ensuring system high availability and scalability", "requirements": ["Bachelor's degree or above in Computer Science or related field", "5+ years of cloud computing architecture design experience", "Familiar with mainstream cloud platforms like AWS, Alibaba Cloud", "Proficient in container technologies like Kubernetes, Docker", "Experience in large-scale distributed system design"], "responsibilities": ["Design cloud computing infrastructure architecture", "Optimize system performance and cost", "Establish technical standards and best practices", "Guide team technical decisions", "Ensure system security and compliance"], "tags": ["Cloud Computing", "Kubernetes", "Architecture Design", "AWS"]}, {"id": "frontend-developer", "title": "Frontend Developer", "department": "Product R&D Department", "location": "Shenzhen", "type": "Full-time", "experience": "2-4 years", "salary": "18K-30K", "description": "Responsible for web frontend development, creating excellent user experience and interface design", "requirements": ["Bachelor's degree or above in Computer Science or related field", "2+ years of frontend development experience", "Proficient in React, Vue.js and other frontend frameworks", "Familiar with TypeScript, HTML5, CSS3", "Mobile development experience preferred"], "responsibilities": ["Develop and maintain web frontend applications", "Collaborate with designers to implement UI/UX designs", "Optimize frontend performance and user experience", "Participate in product requirement analysis and technical solution design", "Write high-quality, maintainable code"], "tags": ["React", "Vue.js", "TypeScript", "Frontend Development"]}, {"id": "product-manager", "title": "Product Manager", "department": "Product Department", "location": "Beijing", "type": "Full-time", "experience": "3-5 years", "salary": "20K-35K", "description": "Responsible for product planning and management, driving product innovation and market success", "requirements": ["Bachelor's degree or above, STEM background preferred", "3+ years of product management experience", "Excellent requirement analysis and product design capabilities", "Familiar with agile development processes", "B2B product experience preferred"], "responsibilities": ["Develop product development strategy and planning", "Collect and analyze user requirements", "Coordinate cross-departmental teams to advance product development", "Monitor product data and user feedback", "Optimize product features and user experience"], "tags": ["Product Management", "Requirement Analysis", "Agile Development", "B2B"]}, {"id": "<PERSON><PERSON><PERSON>-engineer", "title": "DevOps Engineer", "department": "Operations Department", "location": "Hangzhou", "type": "Full-time", "experience": "3-6 years", "salary": "22K-38K", "description": "Responsible for CI/CD process construction and operations automation, ensuring stable system operation", "requirements": ["Bachelor's degree or above in Computer Science or related field", "3+ years of DevOps or operations experience", "Proficient in CI/CD tools like Jenkins, GitLab CI", "Expert in Linux system administration and Shell scripting", "Experience with containerization and microservices architecture"], "responsibilities": ["Build and maintain CI/CD pipelines", "Automate deployment and operations processes", "Monitor system performance and stability", "Handle online incidents and emergency response", "Optimize infrastructure and cost control"], "tags": ["DevOps", "CI/CD", "Linux", "Automation"]}, {"id": "data-scientist", "title": "Data Scientist", "department": "Data Department", "location": "Beijing", "type": "Full-time", "experience": "3-5 years", "salary": "25K-42K", "description": "Responsible for data analysis and mining, providing data support for business decisions", "requirements": ["Master's degree or above in Statistics, Mathematics or related field", "3+ years of data science project experience", "Proficient in Python, R, SQL and other data analysis tools", "Familiar with machine learning algorithms and statistical methods", "Big data processing experience preferred"], "responsibilities": ["Design and execute data analysis projects", "Build predictive models and recommendation algorithms", "Analyze business data and provide insights", "Collaborate with business teams to solve practical problems", "Optimize data processing workflows and efficiency"], "tags": ["Data Science", "Machine Learning", "Python", "Statistical Analysis"]}], "applicationProcess": {"title": "Application Process", "subtitle": "Simple and efficient recruitment process", "steps": [{"step": "1", "title": "Online Application", "description": "Submit resume and cover letter"}, {"step": "2", "title": "Resume Screening", "description": "HR preliminary screening for fit"}, {"step": "3", "title": "Technical Interview", "description": "In-depth communication with technical team"}, {"step": "4", "title": "Comprehensive Interview", "description": "Final interview with management"}, {"step": "5", "title": "Offer Extended", "description": "Confirm onboarding details"}]}, "cta": {"title": "Ready to Join Us?", "subtitle": "Send your resume and start your career journey at ZeroDots", "emailLabel": "Send resume to", "email": "<EMAIL>", "phoneLabel": "Or call", "phone": "+86 ************"}}