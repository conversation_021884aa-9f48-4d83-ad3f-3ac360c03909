{"title": "加入我们", "subtitle": "与零点科技一起创造未来", "description": "我们正在寻找充满激情的人才加入我们的团队，共同推动AI与云计算技术的发展", "hero": {"title": "职业发展机会", "subtitle": "在零点科技开启您的职业新篇章", "description": "加入我们，与行业顶尖人才一起工作，在快速发展的AI与云计算领域实现您的职业目标"}, "whyJoinUs": {"title": "为什么选择零点科技", "subtitle": "我们提供优秀的工作环境和发展机会", "benefits": [{"title": "技术前沿", "description": "参与最新AI技术研发，接触行业前沿项目", "icon": "brain"}, {"title": "团队协作", "description": "与优秀的技术团队合作，共同解决挑战性问题", "icon": "users"}, {"title": "成长空间", "description": "完善的培训体系和职业发展路径", "icon": "trending-up"}, {"title": "工作环境", "description": "现代化办公环境，灵活的工作方式", "icon": "building"}, {"title": "薪酬福利", "description": "具有竞争力的薪酬和完善的福利体系", "icon": "gift"}, {"title": "创新文化", "description": "鼓励创新思维，支持新想法的实现", "icon": "lightbulb"}]}, "openPositions": {"title": "招聘职位", "subtitle": "发现适合您的职业机会", "noPositions": "暂无职位空缺", "viewDetails": "查看详情", "applyNow": "立即申请"}, "positions": [{"id": "senior-ai-engineer", "title": "高级AI工程师", "department": "技术研发部", "location": "北京", "type": "全职", "experience": "3-5年", "salary": "25K-40K", "description": "负责AI算法研发和优化，参与智能标注平台的核心技术开发", "requirements": ["计算机科学或相关专业本科及以上学历", "3年以上机器学习/深度学习项目经验", "熟练掌握Python、TensorFlow/PyTorch等技术栈", "具备良好的算法设计和优化能力", "有计算机视觉或自然语言处理经验优先"], "responsibilities": ["设计和实现AI算法模型", "优化模型性能和准确率", "参与产品技术架构设计", "指导初级工程师技术成长", "跟踪前沿技术发展趋势"], "tags": ["AI", "机器学习", "Python", "深度学习"]}, {"id": "cloud-architect", "title": "云架构师", "department": "基础设施部", "location": "上海", "type": "全职", "experience": "5-8年", "salary": "30K-50K", "description": "负责云计算基础设施设计和优化，确保系统的高可用性和可扩展性", "requirements": ["计算机科学或相关专业本科及以上学历", "5年以上云计算架构设计经验", "熟悉AWS、阿里云等主流云平台", "精通Kubernet<PERSON>、Docker等容器技术", "具备大规模分布式系统设计经验"], "responsibilities": ["设计云计算基础设施架构", "优化系统性能和成本", "制定技术标准和最佳实践", "指导团队技术决策", "确保系统安全性和合规性"], "tags": ["云计算", "Kubernetes", "架构设计", "AWS"]}, {"id": "frontend-developer", "title": "前端开发工程师", "department": "产品研发部", "location": "深圳", "type": "全职", "experience": "2-4年", "salary": "18K-30K", "description": "负责Web前端开发，打造优秀的用户体验和界面设计", "requirements": ["计算机科学或相关专业本科及以上学历", "2年以上前端开发经验", "精通React、Vue.js等前端框架", "熟悉TypeScript、HTML5、CSS3", "有移动端开发经验优先"], "responsibilities": ["开发和维护Web前端应用", "与设计师协作实现UI/UX设计", "优化前端性能和用户体验", "参与产品需求分析和技术方案设计", "编写高质量、可维护的代码"], "tags": ["React", "Vue.js", "TypeScript", "前端开发"]}, {"id": "product-manager", "title": "产品经理", "department": "产品部", "location": "北京", "type": "全职", "experience": "3-5年", "salary": "20K-35K", "description": "负责产品规划和管理，推动产品创新和市场成功", "requirements": ["本科及以上学历，理工科背景优先", "3年以上产品管理经验", "具备优秀的需求分析和产品设计能力", "熟悉敏捷开发流程", "有B2B产品经验优先"], "responsibilities": ["制定产品发展战略和规划", "收集和分析用户需求", "协调跨部门团队推进产品开发", "监控产品数据和用户反馈", "优化产品功能和用户体验"], "tags": ["产品管理", "需求分析", "敏捷开发", "B2B"]}, {"id": "<PERSON><PERSON><PERSON>-engineer", "title": "DevOps工程师", "department": "运维部", "location": "杭州", "type": "全职", "experience": "3-6年", "salary": "22K-38K", "description": "负责CI/CD流程建设和运维自动化，确保系统稳定运行", "requirements": ["计算机科学或相关专业本科及以上学历", "3年以上DevOps或运维经验", "熟练使用Jenkins、GitLab CI等CI/CD工具", "精通Linux系统管理和Shell脚本", "有容器化和微服务架构经验"], "responsibilities": ["建设和维护CI/CD流水线", "自动化部署和运维流程", "监控系统性能和稳定性", "处理线上故障和应急响应", "优化基础设施和成本控制"], "tags": ["DevOps", "CI/CD", "Linux", "自动化"]}, {"id": "data-scientist", "title": "数据科学家", "department": "数据部", "location": "北京", "type": "全职", "experience": "3-5年", "salary": "25K-42K", "description": "负责数据分析和挖掘，为业务决策提供数据支持", "requirements": ["统计学、数学或相关专业硕士及以上学历", "3年以上数据科学项目经验", "精通Python、R、SQL等数据分析工具", "熟悉机器学习算法和统计方法", "有大数据处理经验优先"], "responsibilities": ["设计和执行数据分析项目", "建立预测模型和推荐算法", "分析业务数据并提供洞察", "与业务团队协作解决实际问题", "优化数据处理流程和效率"], "tags": ["数据科学", "机器学习", "Python", "统计分析"]}], "applicationProcess": {"title": "申请流程", "subtitle": "简单高效的招聘流程", "steps": [{"step": "1", "title": "在线申请", "description": "提交简历和求职信"}, {"step": "2", "title": "简历筛选", "description": "HR初步筛选匹配度"}, {"step": "3", "title": "技术面试", "description": "技术团队深度交流"}, {"step": "4", "title": "综合面试", "description": "管理层最终面试"}, {"step": "5", "title": "发放Offer", "description": "确认入职相关事宜"}]}, "cta": {"title": "准备好加入我们了吗？", "subtitle": "发送您的简历，开始您在零点科技的职业之旅", "emailLabel": "发送简历至", "email": "<EMAIL>", "phoneLabel": "或致电", "phone": "+86 ************"}}